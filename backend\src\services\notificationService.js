const { Notification, User, Driver } = require('../models');
const { NOTIFICATION_TYPES, NOTIFICATION_STATUS } = require('../config/constants');
const socketService = require('./socketService');
const logger = require('../config/logger');

class NotificationService {
  /**
   * Send notification to user
   * @param {Object} notificationData - Notification data
   * @returns {Promise<Object>} Created notification
   */
  async sendNotification(notificationData) {
    try {
      const {
        recipient,
        type,
        title,
        message,
        category = 'general',
        priority = 'medium',
        channels = ['in_app'],
        relatedEntity,
        data = {},
        expiresAt,
        scheduledFor
      } = notificationData;

      // Create notification channels
      const notificationChannels = channels.map(channel => ({
        type: channel,
        status: 'pending'
      }));

      // Create notification
      const notification = new Notification({
        recipient,
        type,
        title,
        message,
        category,
        priority,
        channels: notificationChannels,
        relatedEntity,
        data,
        expiresAt,
        scheduledFor: scheduledFor || new Date()
      });

      await notification.save();

      // Send through different channels
      await this.processNotificationChannels(notification);

      logger.info(`Notification sent: ${notification._id} to user: ${recipient}`);
      
      return notification;
    } catch (error) {
      logger.error('Send notification error:', error);
      throw error;
    }
  }

  /**
   * Process notification through different channels
   * @param {Object} notification - Notification object
   */
  async processNotificationChannels(notification) {
    try {
      for (const channel of notification.channels) {
        switch (channel.type) {
          case 'in_app':
            await this.sendInAppNotification(notification);
            break;
          case 'email':
            await this.sendEmailNotification(notification);
            break;
          case 'sms':
            await this.sendSMSNotification(notification);
            break;
          case 'push':
            await this.sendPushNotification(notification);
            break;
          default:
            logger.warn(`Unknown notification channel: ${channel.type}`);
        }
      }
    } catch (error) {
      logger.error('Process notification channels error:', error);
    }
  }

  /**
   * Send in-app notification via WebSocket
   * @param {Object} notification - Notification object
   */
  async sendInAppNotification(notification) {
    try {
      // Send via WebSocket if user is online
      socketService.sendNotificationToUser(notification.recipient, {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        category: notification.category,
        priority: notification.priority,
        data: notification.data,
        createdAt: notification.createdAt
      });

      // Update channel status
      await this.updateChannelStatus(notification._id, 'in_app', 'sent');
      
      logger.info(`In-app notification sent: ${notification._id}`);
    } catch (error) {
      logger.error('Send in-app notification error:', error);
      await this.updateChannelStatus(notification._id, 'in_app', 'failed', error.message);
    }
  }

  /**
   * Send email notification
   * @param {Object} notification - Notification object
   */
  async sendEmailNotification(notification) {
    try {
      // Get user details
      const user = await User.findById(notification.recipient);
      if (!user || !user.email) {
        throw new Error('User email not found');
      }

      // Check user email preferences
      if (!user.preferences?.notifications?.email) {
        await this.updateChannelStatus(notification._id, 'email', 'skipped', 'User disabled email notifications');
        return;
      }

      // TODO: Integrate with email service (SendGrid, AWS SES, etc.)
      // For now, we'll simulate email sending
      const emailData = {
        to: user.email,
        subject: notification.title,
        html: this.generateEmailTemplate(notification, user),
        category: notification.category
      };

      // Simulate email sending
      await this.simulateEmailSending(emailData);

      await this.updateChannelStatus(notification._id, 'email', 'sent');
      logger.info(`Email notification sent: ${notification._id} to ${user.email}`);
    } catch (error) {
      logger.error('Send email notification error:', error);
      await this.updateChannelStatus(notification._id, 'email', 'failed', error.message);
    }
  }

  /**
   * Send SMS notification
   * @param {Object} notification - Notification object
   */
  async sendSMSNotification(notification) {
    try {
      // Get user details
      const user = await User.findById(notification.recipient);
      if (!user || !user.phone) {
        throw new Error('User phone not found');
      }

      // Check user SMS preferences
      if (!user.preferences?.notifications?.sms) {
        await this.updateChannelStatus(notification._id, 'sms', 'skipped', 'User disabled SMS notifications');
        return;
      }

      // TODO: Integrate with SMS service (Twilio, AWS SNS, etc.)
      // For now, we'll simulate SMS sending
      const smsData = {
        to: user.phone,
        message: `${notification.title}: ${notification.message}`,
        category: notification.category
      };

      // Simulate SMS sending
      await this.simulateSMSSending(smsData);

      await this.updateChannelStatus(notification._id, 'sms', 'sent');
      logger.info(`SMS notification sent: ${notification._id} to ${user.phone}`);
    } catch (error) {
      logger.error('Send SMS notification error:', error);
      await this.updateChannelStatus(notification._id, 'sms', 'failed', error.message);
    }
  }

  /**
   * Send push notification
   * @param {Object} notification - Notification object
   */
  async sendPushNotification(notification) {
    try {
      // Get user details
      const user = await User.findById(notification.recipient);
      if (!user) {
        throw new Error('User not found');
      }

      // Check user push preferences
      if (!user.preferences?.notifications?.push) {
        await this.updateChannelStatus(notification._id, 'push', 'skipped', 'User disabled push notifications');
        return;
      }

      // TODO: Integrate with push notification service (Firebase, AWS SNS, etc.)
      // For now, we'll simulate push notification sending
      const pushData = {
        userId: user._id,
        title: notification.title,
        body: notification.message,
        data: notification.data,
        category: notification.category
      };

      // Simulate push notification sending
      await this.simulatePushSending(pushData);

      await this.updateChannelStatus(notification._id, 'push', 'sent');
      logger.info(`Push notification sent: ${notification._id} to user ${user._id}`);
    } catch (error) {
      logger.error('Send push notification error:', error);
      await this.updateChannelStatus(notification._id, 'push', 'failed', error.message);
    }
  }

  /**
   * Update notification channel status
   * @param {string} notificationId - Notification ID
   * @param {string} channelType - Channel type
   * @param {string} status - New status
   * @param {string} error - Error message if failed
   */
  async updateChannelStatus(notificationId, channelType, status, error = null) {
    try {
      const updateData = {
        'channels.$.status': status,
        'channels.$.sentAt': status === 'sent' ? new Date() : undefined,
        'channels.$.error': error
      };

      await Notification.updateOne(
        { _id: notificationId, 'channels.type': channelType },
        { $set: updateData }
      );
    } catch (err) {
      logger.error('Update channel status error:', err);
    }
  }

  /**
   * Generate email template
   * @param {Object} notification - Notification object
   * @param {Object} user - User object
   * @returns {string} HTML email template
   */
  generateEmailTemplate(notification, user) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${notification.title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #007bff; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Parter Logistics</h1>
          </div>
          <div class="content">
            <h2>${notification.title}</h2>
            <p>Hi ${user.firstName},</p>
            <p>${notification.message}</p>
            ${notification.data.actionUrl ? `<p><a href="${notification.data.actionUrl}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Details</a></p>` : ''}
          </div>
          <div class="footer">
            <p>This is an automated message from Parter Logistics. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Simulate email sending (replace with actual email service)
   */
  async simulateEmailSending(emailData) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
    logger.info(`[SIMULATED] Email sent to: ${emailData.to}`);
  }

  /**
   * Simulate SMS sending (replace with actual SMS service)
   */
  async simulateSMSSending(smsData) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
    logger.info(`[SIMULATED] SMS sent to: ${smsData.to}`);
  }

  /**
   * Simulate push notification sending (replace with actual push service)
   */
  async simulatePushSending(pushData) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));
    logger.info(`[SIMULATED] Push notification sent to user: ${pushData.userId}`);
  }

  /**
   * Send booking-related notifications
   */
  async sendBookingNotification(bookingId, type, recipientId, additionalData = {}) {
    try {
      const notificationTemplates = {
        'booking_created': {
          title: 'Booking Confirmed',
          message: `Your booking ${bookingId} has been confirmed and we're finding a driver for you.`,
          category: 'booking',
          channels: ['in_app', 'email', 'push']
        },
        'driver_assigned': {
          title: 'Driver Assigned',
          message: `A driver has been assigned to your booking ${bookingId}. They will arrive shortly.`,
          category: 'booking',
          channels: ['in_app', 'sms', 'push']
        },
        'driver_arrived': {
          title: 'Driver Arrived',
          message: `Your driver has arrived at the pickup location for booking ${bookingId}.`,
          category: 'booking',
          channels: ['in_app', 'sms', 'push']
        },
        'trip_started': {
          title: 'Trip Started',
          message: `Your trip has started for booking ${bookingId}. Track your delivery in real-time.`,
          category: 'booking',
          channels: ['in_app', 'push']
        },
        'trip_completed': {
          title: 'Trip Completed',
          message: `Your booking ${bookingId} has been completed successfully. Please rate your experience.`,
          category: 'booking',
          channels: ['in_app', 'email', 'push']
        },
        'booking_cancelled': {
          title: 'Booking Cancelled',
          message: `Your booking ${bookingId} has been cancelled. Any applicable refunds will be processed.`,
          category: 'booking',
          channels: ['in_app', 'email', 'push']
        }
      };

      const template = notificationTemplates[type];
      if (!template) {
        throw new Error(`Unknown notification type: ${type}`);
      }

      await this.sendNotification({
        recipient: recipientId,
        type,
        title: template.title,
        message: template.message,
        category: template.category,
        channels: template.channels,
        relatedEntity: {
          entityType: 'booking',
          entityId: bookingId
        },
        data: {
          bookingId,
          ...additionalData
        }
      });
    } catch (error) {
      logger.error('Send booking notification error:', error);
    }
  }

  /**
   * Send bulk notifications
   * @param {Array} recipients - Array of recipient IDs
   * @param {Object} notificationData - Notification data
   */
  async sendBulkNotifications(recipients, notificationData) {
    try {
      const notifications = recipients.map(recipientId => ({
        ...notificationData,
        recipient: recipientId
      }));

      // Process in batches to avoid overwhelming the system
      const batchSize = 50;
      for (let i = 0; i < notifications.length; i += batchSize) {
        const batch = notifications.slice(i, i + batchSize);
        await Promise.all(batch.map(notification => this.sendNotification(notification)));
      }

      logger.info(`Bulk notifications sent to ${recipients.length} recipients`);
    } catch (error) {
      logger.error('Send bulk notifications error:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new NotificationService();
