const { Notification, User } = require('../models');
const { RESPONSE_MESSAGES, NOTIFICATION_STATUS, PAGINATION } = require('../config/constants');
const notificationService = require('../services/notificationService');
const logger = require('../config/logger');

/**
 * Get user notifications
 */
const getUserNotifications = async (req, res) => {
  try {
    const userId = req.user._id;
    const {
      page = PAGINATION.DEFAULT_PAGE,
      limit = PAGINATION.DEFAULT_LIMIT,
      category,
      priority,
      isRead,
      startDate,
      endDate
    } = req.query;

    const query = { recipient: userId };
    
    if (category) query.category = category;
    if (priority) query.priority = priority;
    if (typeof isRead === 'boolean') query.isRead = isRead;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Exclude expired notifications
    query.$or = [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ];

    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Notification.countDocuments(query);
    const unreadCount = await Notification.countDocuments({
      recipient: userId,
      isRead: false,
      $or: [
        { expiresAt: { $exists: false } },
        { expiresAt: { $gt: new Date() } }
      ]
    });

    res.json({
      success: true,
      data: {
        notifications,
        unreadCount,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get user notifications error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get notification details
 */
const getNotificationDetails = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    const notification = await Notification.findOne({
      _id: notificationId,
      recipient: userId
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    // Mark as read if not already read
    if (!notification.isRead) {
      notification.isRead = true;
      notification.readAt = new Date();
      await notification.save();
    }

    res.json({
      success: true,
      data: { notification }
    });
  } catch (error) {
    logger.error('Get notification details error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Mark notification as read
 */
const markAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    const notification = await Notification.findOneAndUpdate(
      { _id: notificationId, recipient: userId },
      { 
        isRead: true,
        readAt: new Date()
      },
      { new: true }
    );

    if (!notification) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification marked as read',
      data: { notification }
    });
  } catch (error) {
    logger.error('Mark notification as read error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Mark all notifications as read
 */
const markAllAsRead = async (req, res) => {
  try {
    const userId = req.user._id;

    const result = await Notification.updateMany(
      { 
        recipient: userId,
        isRead: false
      },
      { 
        isRead: true,
        readAt: new Date()
      }
    );

    res.json({
      success: true,
      message: `${result.modifiedCount} notifications marked as read`
    });
  } catch (error) {
    logger.error('Mark all notifications as read error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Delete notification
 */
const deleteNotification = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    const notification = await Notification.findOneAndDelete({
      _id: notificationId,
      recipient: userId
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        error: 'Notification not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    logger.error('Delete notification error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Update notification preferences
 */
const updateNotificationPreferences = async (req, res) => {
  try {
    const userId = req.user._id;
    const preferences = req.body;

    const user = await User.findByIdAndUpdate(
      userId,
      { 
        $set: { 
          'preferences.notifications': preferences 
        } 
      },
      { new: true, runValidators: true }
    ).select('preferences.notifications');

    logger.info(`Notification preferences updated for user: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Notification preferences updated successfully',
      data: { 
        preferences: user.preferences.notifications 
      }
    });
  } catch (error) {
    logger.error('Update notification preferences error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get notification preferences
 */
const getNotificationPreferences = async (req, res) => {
  try {
    const userId = req.user._id;

    const user = await User.findById(userId).select('preferences.notifications');
    
    res.json({
      success: true,
      data: { 
        preferences: user.preferences?.notifications || {
          email: true,
          sms: true,
          push: true
        }
      }
    });
  } catch (error) {
    logger.error('Get notification preferences error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Send test notification (Admin only)
 */
const sendTestNotification = async (req, res) => {
  try {
    const { recipientId, title, message, channels, category } = req.body;

    const notification = await notificationService.sendNotification({
      recipient: recipientId,
      type: 'test',
      title: title || 'Test Notification',
      message: message || 'This is a test notification from the admin panel.',
      category: category || 'system',
      channels: channels || ['in_app'],
      data: {
        sentBy: req.user._id,
        sentAt: new Date()
      }
    });

    logger.info(`Test notification sent by admin: ${req.user.email} to user: ${recipientId}`);

    res.json({
      success: true,
      message: 'Test notification sent successfully',
      data: { notification }
    });
  } catch (error) {
    logger.error('Send test notification error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Send bulk notification (Admin only)
 */
const sendBulkNotification = async (req, res) => {
  try {
    const { 
      recipients, 
      title, 
      message, 
      channels, 
      category,
      userRole,
      city 
    } = req.body;

    let recipientIds = recipients;

    // If no specific recipients provided, find users based on criteria
    if (!recipients || recipients.length === 0) {
      const query = {};
      if (userRole) query.role = userRole;
      if (city) query['addresses.city'] = city;

      const users = await User.find(query).select('_id');
      recipientIds = users.map(user => user._id);
    }

    if (recipientIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No recipients found'
      });
    }

    await notificationService.sendBulkNotifications(recipientIds, {
      type: 'bulk',
      title,
      message,
      category: category || 'announcement',
      channels: channels || ['in_app'],
      data: {
        sentBy: req.user._id,
        sentAt: new Date()
      }
    });

    logger.info(`Bulk notification sent by admin: ${req.user.email} to ${recipientIds.length} users`);

    res.json({
      success: true,
      message: `Bulk notification sent to ${recipientIds.length} users`
    });
  } catch (error) {
    logger.error('Send bulk notification error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get notification statistics (Admin only)
 */
const getNotificationStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.$gte = new Date(startDate);
      if (endDate) dateFilter.createdAt.$lte = new Date(endDate);
    }

    const [
      totalNotifications,
      readNotifications,
      notificationsByCategory,
      notificationsByChannel,
      recentNotifications
    ] = await Promise.all([
      Notification.countDocuments(dateFilter),
      Notification.countDocuments({ ...dateFilter, isRead: true }),
      Notification.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$category', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      Notification.aggregate([
        { $match: dateFilter },
        { $unwind: '$channels' },
        { $group: { _id: '$channels.type', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      Notification.find(dateFilter)
        .sort({ createdAt: -1 })
        .limit(10)
        .populate('recipient', 'firstName lastName email')
    ]);

    res.json({
      success: true,
      data: {
        summary: {
          total: totalNotifications,
          read: readNotifications,
          unread: totalNotifications - readNotifications,
          readRate: totalNotifications > 0 ? Math.round((readNotifications / totalNotifications) * 100) : 0
        },
        byCategory: notificationsByCategory,
        byChannel: notificationsByChannel,
        recent: recentNotifications
      }
    });
  } catch (error) {
    logger.error('Get notification stats error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

module.exports = {
  getUserNotifications,
  getNotificationDetails,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  updateNotificationPreferences,
  getNotificationPreferences,
  sendTestNotification,
  sendBulkNotification,
  getNotificationStats
};
