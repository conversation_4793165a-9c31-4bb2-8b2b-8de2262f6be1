{"name": "xss-filters", "version": "1.2.7", "homepage": "https://github.com/yahoo/xss-filters", "authors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "description": "Just sufficient output filtering to prevent XSS", "main": "dist/xss-filters.min.js", "moduleType": ["node"], "keywords": ["xss", "output filter", "sanitize", "sanitise", "escape", "encode", "filter", "context-aware", "context-sensitive", "security", "yahoo"], "license": "BSD", "ignore": ["node_modules", "bower_components", "src", "tests", "artifacts", "*.js", "jsdoc.conf.json", ".travis.yml", ".giti<PERSON>re", "package.json"]}