const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { User, Driver, Booking } = require('../models');
const logger = require('../config/logger');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
    this.connectedDrivers = new Map(); // driverId -> socketId
    this.bookingRooms = new Map(); // bookingId -> [userSocketId, driverSocketId]
  }

  /**
   * Initialize Socket.IO server
   */
  init(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.userId).select('-password');
        
        if (!user || !user.isActive) {
          return next(new Error('Invalid or inactive user'));
        }

        socket.userId = user._id.toString();
        socket.userRole = user.role;
        socket.user = user;

        // Get driver info if user is a driver
        if (user.role === 'driver') {
          const driver = await Driver.findOne({ user: user._id });
          if (driver) {
            socket.driverId = driver._id.toString();
            socket.driver = driver;
          }
        }

        next();
      } catch (error) {
        logger.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    // Connection handling
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    logger.info('Socket.IO server initialized');
  }

  /**
   * Handle new socket connection
   */
  handleConnection(socket) {
    const { userId, userRole, driverId } = socket;
    
    logger.info(`User connected: ${userId} (${userRole}) - Socket: ${socket.id}`);

    // Store connection
    this.connectedUsers.set(userId, socket.id);
    if (driverId) {
      this.connectedDrivers.set(driverId, socket.id);
    }

    // Join user to their personal room
    socket.join(`user:${userId}`);
    if (driverId) {
      socket.join(`driver:${driverId}`);
    }

    // Set up event handlers
    this.setupEventHandlers(socket);

    // Send connection confirmation
    socket.emit('connected', {
      userId,
      userRole,
      driverId,
      timestamp: new Date()
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });
  }

  /**
   * Set up event handlers for socket
   */
  setupEventHandlers(socket) {
    const { userId, userRole, driverId } = socket;

    // Join booking room
    socket.on('join_booking', async (data) => {
      try {
        const { bookingId } = data;
        const booking = await Booking.findOne({ bookingId });
        
        if (!booking) {
          socket.emit('error', { message: 'Booking not found' });
          return;
        }

        // Check permissions
        const canJoin = (
          (userRole === 'user' && booking.user.toString() === userId) ||
          (userRole === 'driver' && booking.driver?.toString() === driverId) ||
          userRole === 'admin'
        );

        if (!canJoin) {
          socket.emit('error', { message: 'Access denied' });
          return;
        }

        socket.join(`booking:${bookingId}`);
        
        // Track booking room participants
        if (!this.bookingRooms.has(bookingId)) {
          this.bookingRooms.set(bookingId, []);
        }
        this.bookingRooms.get(bookingId).push(socket.id);

        socket.emit('booking_joined', { bookingId });
        logger.info(`User ${userId} joined booking room: ${bookingId}`);
      } catch (error) {
        logger.error('Join booking error:', error);
        socket.emit('error', { message: 'Failed to join booking' });
      }
    });

    // Leave booking room
    socket.on('leave_booking', (data) => {
      const { bookingId } = data;
      socket.leave(`booking:${bookingId}`);
      
      // Remove from booking room tracking
      if (this.bookingRooms.has(bookingId)) {
        const participants = this.bookingRooms.get(bookingId);
        const index = participants.indexOf(socket.id);
        if (index > -1) {
          participants.splice(index, 1);
        }
        if (participants.length === 0) {
          this.bookingRooms.delete(bookingId);
        }
      }

      socket.emit('booking_left', { bookingId });
    });

    // Driver location update
    if (userRole === 'driver') {
      socket.on('location_update', async (data) => {
        try {
          const { latitude, longitude, heading, speed } = data;
          
          // Update driver location in database
          await Driver.findByIdAndUpdate(driverId, {
            currentLocation: {
              type: 'Point',
              coordinates: [longitude, latitude]
            },
            lastLocationUpdate: new Date(),
            'locationData.heading': heading,
            'locationData.speed': speed
          });

          // Broadcast to active bookings
          const activeBookings = await Booking.find({
            driver: driverId,
            status: { $in: ['driver_assigned', 'driver_arrived', 'pickup_completed', 'in_transit'] }
          });

          activeBookings.forEach(booking => {
            this.io.to(`booking:${booking.bookingId}`).emit('driver_location_update', {
              bookingId: booking.bookingId,
              location: { latitude, longitude },
              heading,
              speed,
              timestamp: new Date()
            });
          });

        } catch (error) {
          logger.error('Location update error:', error);
          socket.emit('error', { message: 'Failed to update location' });
        }
      });

      // Driver status update
      socket.on('status_update', async (data) => {
        try {
          const { status } = data;
          
          await Driver.findByIdAndUpdate(driverId, {
            status,
            lastActiveAt: new Date()
          });

          // Broadcast status update
          socket.broadcast.emit('driver_status_update', {
            driverId,
            status,
            timestamp: new Date()
          });

        } catch (error) {
          logger.error('Status update error:', error);
          socket.emit('error', { message: 'Failed to update status' });
        }
      });
    }

    // Typing indicators for chat
    socket.on('typing_start', (data) => {
      const { bookingId } = data;
      socket.to(`booking:${bookingId}`).emit('user_typing', {
        userId,
        userRole,
        timestamp: new Date()
      });
    });

    socket.on('typing_stop', (data) => {
      const { bookingId } = data;
      socket.to(`booking:${bookingId}`).emit('user_stopped_typing', {
        userId,
        userRole,
        timestamp: new Date()
      });
    });

    // Chat messages
    socket.on('send_message', async (data) => {
      try {
        const { bookingId, message, messageType = 'text' } = data;
        
        const booking = await Booking.findOne({ bookingId });
        if (!booking) {
          socket.emit('error', { message: 'Booking not found' });
          return;
        }

        // Check permissions
        const canSend = (
          (userRole === 'user' && booking.user.toString() === userId) ||
          (userRole === 'driver' && booking.driver?.toString() === driverId)
        );

        if (!canSend) {
          socket.emit('error', { message: 'Access denied' });
          return;
        }

        // Save message to booking
        const messageData = {
          sender: userId,
          senderType: userRole,
          message,
          messageType,
          timestamp: new Date()
        };

        booking.messages.push(messageData);
        await booking.save();

        // Broadcast message to booking room
        this.io.to(`booking:${bookingId}`).emit('new_message', {
          bookingId,
          ...messageData,
          senderName: `${socket.user.firstName} ${socket.user.lastName}`
        });

      } catch (error) {
        logger.error('Send message error:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });
  }

  /**
   * Handle socket disconnection
   */
  handleDisconnection(socket) {
    const { userId, driverId } = socket;
    
    logger.info(`User disconnected: ${userId} - Socket: ${socket.id}`);

    // Remove from connected users
    this.connectedUsers.delete(userId);
    if (driverId) {
      this.connectedDrivers.delete(driverId);
    }

    // Clean up booking rooms
    this.bookingRooms.forEach((participants, bookingId) => {
      const index = participants.indexOf(socket.id);
      if (index > -1) {
        participants.splice(index, 1);
        if (participants.length === 0) {
          this.bookingRooms.delete(bookingId);
        }
      }
    });
  }

  /**
   * Send notification to user
   */
  sendNotificationToUser(userId, notification) {
    const socketId = this.connectedUsers.get(userId.toString());
    if (socketId) {
      this.io.to(socketId).emit('notification', notification);
    }
  }

  /**
   * Send notification to driver
   */
  sendNotificationToDriver(driverId, notification) {
    const socketId = this.connectedDrivers.get(driverId.toString());
    if (socketId) {
      this.io.to(socketId).emit('notification', notification);
    }
  }

  /**
   * Broadcast booking update
   */
  broadcastBookingUpdate(bookingId, update) {
    this.io.to(`booking:${bookingId}`).emit('booking_update', {
      bookingId,
      ...update,
      timestamp: new Date()
    });
  }

  /**
   * Send driver assignment notification
   */
  notifyDriverAssignment(bookingId, driverData, userData) {
    this.io.to(`booking:${bookingId}`).emit('driver_assigned', {
      bookingId,
      driver: driverData,
      user: userData,
      timestamp: new Date()
    });
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount() {
    return {
      totalUsers: this.connectedUsers.size,
      totalDrivers: this.connectedDrivers.size,
      activeBookings: this.bookingRooms.size
    };
  }

  /**
   * Check if user is online
   */
  isUserOnline(userId) {
    return this.connectedUsers.has(userId.toString());
  }

  /**
   * Check if driver is online
   */
  isDriverOnline(driverId) {
    return this.connectedDrivers.has(driverId.toString());
  }
}

// Export singleton instance
module.exports = new SocketService();
