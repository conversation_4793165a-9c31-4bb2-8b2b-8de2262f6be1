const express = require('express');
const router = express.Router();

// Import controllers
const {
  createBookingPaymentIntent,
  confirmBookingPayment,
  getUserPayments,
  getPaymentDetails,
  requestRefund,
  addUserPaymentMethod,
  getUserPaymentMethods,
  removeUserPaymentMethod
} = require('../controllers/paymentController');

// Import middleware
const {
  authenticate,
  authorize,
  requireVerification,
  adminOrOwner
} = require('../middleware/auth');

// Import validators
const {
  validate,
  validateQuery,
  createPaymentIntentValidation,
  confirmPaymentValidation,
  refundRequestValidation,
  addPaymentMethodValidation,
  paymentQueryValidation
} = require('../validators/paymentValidator');

/**
 * @route   POST /api/v1/payments/bookings/:bookingId/intent
 * @desc    Create payment intent for booking
 * @access  Private (User role)
 */
router.post('/bookings/:bookingId/intent',
  authenticate,
  authorize('user'),
  requireVerification,
  validate(createPaymentIntentValidation),
  createBookingPaymentIntent
);

/**
 * @route   POST /api/v1/payments/bookings/:bookingId/confirm
 * @desc    Confirm payment for booking
 * @access  Private (User role)
 */
router.post('/bookings/:bookingId/confirm',
  authenticate,
  authorize('user'),
  requireVerification,
  validate(confirmPaymentValidation),
  confirmBookingPayment
);

/**
 * @route   GET /api/v1/payments
 * @desc    Get user payments
 * @access  Private
 */
router.get('/',
  authenticate,
  validateQuery(paymentQueryValidation),
  getUserPayments
);

/**
 * @route   GET /api/v1/payments/:paymentId
 * @desc    Get payment details
 * @access  Private
 */
router.get('/:paymentId',
  authenticate,
  getPaymentDetails
);

/**
 * @route   POST /api/v1/payments/:paymentId/refund
 * @desc    Request refund
 * @access  Private (User role or Admin)
 */
router.post('/:paymentId/refund',
  authenticate,
  validate(refundRequestValidation),
  requestRefund
);

/**
 * @route   POST /api/v1/payments/methods
 * @desc    Add payment method
 * @access  Private (User role)
 */
router.post('/methods',
  authenticate,
  authorize('user'),
  requireVerification,
  validate(addPaymentMethodValidation),
  addUserPaymentMethod
);

/**
 * @route   GET /api/v1/payments/methods
 * @desc    Get user payment methods
 * @access  Private (User role)
 */
router.get('/methods',
  authenticate,
  authorize('user'),
  getUserPaymentMethods
);

/**
 * @route   DELETE /api/v1/payments/methods/:paymentMethodId
 * @desc    Remove payment method
 * @access  Private (User role)
 */
router.delete('/methods/:paymentMethodId',
  authenticate,
  authorize('user'),
  removeUserPaymentMethod
);

module.exports = router;
