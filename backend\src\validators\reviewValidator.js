const Joi = require('joi');

// Create review validation
const createReviewValidation = Joi.object({
  rating: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .required()
    .messages({
      'number.min': 'Rating must be between 1 and 5',
      'number.max': 'Rating must be between 1 and 5',
      'any.required': 'Rating is required'
    }),
  comment: Joi.string()
    .optional()
    .trim()
    .min(5)
    .max(1000)
    .messages({
      'string.min': 'Comment must be at least 5 characters long',
      'string.max': 'Comment cannot exceed 1000 characters'
    }),
  categories: Joi.object({
    punctuality: Joi.number().integer().min(1).max(5).optional(),
    communication: Joi.number().integer().min(1).max(5).optional(),
    professionalism: Joi.number().integer().min(1).max(5).optional(),
    vehicleCondition: Joi.number().integer().min(1).max(5).optional(),
    safety: Joi.number().integer().min(1).max(5).optional(),
    courtesy: Joi.number().integer().min(1).max(5).optional(),
    efficiency: Joi.number().integer().min(1).max(5).optional()
  }).optional(),
  tags: Joi.array()
    .items(Joi.string().trim().min(2).max(50))
    .max(10)
    .optional()
    .messages({
      'array.max': 'Maximum 10 tags allowed',
      'string.min': 'Each tag must be at least 2 characters',
      'string.max': 'Each tag cannot exceed 50 characters'
    }),
  isAnonymous: Joi.boolean()
    .default(false)
});

// Update review validation
const updateReviewValidation = Joi.object({
  rating: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .required()
    .messages({
      'number.min': 'Rating must be between 1 and 5',
      'number.max': 'Rating must be between 1 and 5',
      'any.required': 'Rating is required'
    }),
  comment: Joi.string()
    .optional()
    .trim()
    .min(5)
    .max(1000)
    .messages({
      'string.min': 'Comment must be at least 5 characters long',
      'string.max': 'Comment cannot exceed 1000 characters'
    }),
  categories: Joi.object({
    punctuality: Joi.number().integer().min(1).max(5).optional(),
    communication: Joi.number().integer().min(1).max(5).optional(),
    professionalism: Joi.number().integer().min(1).max(5).optional(),
    vehicleCondition: Joi.number().integer().min(1).max(5).optional(),
    safety: Joi.number().integer().min(1).max(5).optional(),
    courtesy: Joi.number().integer().min(1).max(5).optional(),
    efficiency: Joi.number().integer().min(1).max(5).optional()
  }).optional(),
  tags: Joi.array()
    .items(Joi.string().trim().min(2).max(50))
    .max(10)
    .optional()
    .messages({
      'array.max': 'Maximum 10 tags allowed',
      'string.min': 'Each tag must be at least 2 characters',
      'string.max': 'Each tag cannot exceed 50 characters'
    })
});

// Report review validation
const reportReviewValidation = Joi.object({
  reason: Joi.string()
    .valid('inappropriate_content', 'spam', 'fake_review', 'harassment', 'discrimination', 'other')
    .required()
    .messages({
      'any.only': 'Invalid report reason',
      'any.required': 'Report reason is required'
    }),
  description: Joi.string()
    .optional()
    .trim()
    .min(10)
    .max(500)
    .messages({
      'string.min': 'Description must be at least 10 characters long',
      'string.max': 'Description cannot exceed 500 characters'
    })
});

// Review query validation
const reviewQueryValidation = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10),
  rating: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .optional(),
  reviewerType: Joi.string()
    .valid('user', 'driver')
    .optional(),
  startDate: Joi.date()
    .optional(),
  endDate: Joi.date()
    .greater(Joi.ref('startDate'))
    .optional()
    .messages({
      'date.greater': 'End date must be after start date'
    }),
  sortBy: Joi.string()
    .valid('createdAt', 'rating', 'helpful')
    .default('createdAt'),
  sortOrder: Joi.string()
    .valid('asc', 'desc')
    .default('desc')
});

// Review moderation validation (Admin only)
const reviewModerationValidation = Joi.object({
  moderationStatus: Joi.string()
    .valid('approved', 'rejected', 'flagged')
    .required()
    .messages({
      'any.only': 'Invalid moderation status',
      'any.required': 'Moderation status is required'
    }),
  moderationNotes: Joi.string()
    .optional()
    .trim()
    .max(500)
    .messages({
      'string.max': 'Moderation notes cannot exceed 500 characters'
    }),
  hideFromPublic: Joi.boolean()
    .default(false)
});

// Review response validation (for reviewee to respond)
const reviewResponseValidation = Joi.object({
  response: Joi.string()
    .required()
    .trim()
    .min(10)
    .max(500)
    .messages({
      'string.min': 'Response must be at least 10 characters long',
      'string.max': 'Response cannot exceed 500 characters',
      'any.required': 'Response is required'
    })
});

// Review helpful validation (for marking reviews as helpful)
const reviewHelpfulValidation = Joi.object({
  isHelpful: Joi.boolean()
    .required()
    .messages({
      'any.required': 'Helpful status is required'
    })
});

// Review analytics validation
const reviewAnalyticsValidation = Joi.object({
  startDate: Joi.date()
    .optional(),
  endDate: Joi.date()
    .greater(Joi.ref('startDate'))
    .optional()
    .messages({
      'date.greater': 'End date must be after start date'
    }),
  groupBy: Joi.string()
    .valid('day', 'week', 'month')
    .default('day'),
  revieweeType: Joi.string()
    .valid('user', 'driver')
    .optional(),
  rating: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .optional()
});

// Review template validation (for common review templates)
const reviewTemplateValidation = Joi.object({
  name: Joi.string()
    .required()
    .trim()
    .min(2)
    .max(100)
    .messages({
      'string.min': 'Template name must be at least 2 characters',
      'string.max': 'Template name cannot exceed 100 characters',
      'any.required': 'Template name is required'
    }),
  category: Joi.string()
    .valid('positive', 'negative', 'neutral')
    .required()
    .messages({
      'any.only': 'Invalid template category',
      'any.required': 'Template category is required'
    }),
  rating: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .required()
    .messages({
      'number.min': 'Rating must be between 1 and 5',
      'number.max': 'Rating must be between 1 and 5',
      'any.required': 'Rating is required'
    }),
  comment: Joi.string()
    .required()
    .trim()
    .min(10)
    .max(500)
    .messages({
      'string.min': 'Comment must be at least 10 characters long',
      'string.max': 'Comment cannot exceed 500 characters',
      'any.required': 'Comment is required'
    }),
  tags: Joi.array()
    .items(Joi.string().trim().min(2).max(50))
    .max(10)
    .optional(),
  isActive: Joi.boolean()
    .default(true)
});

// Review filter validation
const reviewFilterValidation = Joi.object({
  minRating: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .optional(),
  maxRating: Joi.number()
    .integer()
    .min(1)
    .max(5)
    .greater(Joi.ref('minRating'))
    .optional()
    .messages({
      'number.greater': 'Max rating must be greater than min rating'
    }),
  hasComment: Joi.boolean()
    .optional(),
  tags: Joi.array()
    .items(Joi.string().trim())
    .optional(),
  reviewerType: Joi.string()
    .valid('user', 'driver')
    .optional(),
  moderationStatus: Joi.string()
    .valid('pending', 'approved', 'rejected', 'flagged')
    .optional(),
  isAnonymous: Joi.boolean()
    .optional(),
  hasResponse: Joi.boolean()
    .optional()
});

// Validation middleware
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    req.body = value;
    next();
  };
};

// Query validation middleware
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Query validation failed',
        details: errors
      });
    }

    req.query = value;
    next();
  };
};

module.exports = {
  createReviewValidation,
  updateReviewValidation,
  reportReviewValidation,
  reviewQueryValidation,
  reviewModerationValidation,
  reviewResponseValidation,
  reviewHelpfulValidation,
  reviewAnalyticsValidation,
  reviewTemplateValidation,
  reviewFilterValidation,
  validate,
  validateQuery
};
