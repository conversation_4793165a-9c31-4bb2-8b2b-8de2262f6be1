const express = require('express');
const router = express.Router();

// Import controllers
const {
  getBookingTracking,
  updateDriverLocation,
  getNearbyDrivers,
  getDriverLocationHistory,
  getTrackingStats
} = require('../controllers/trackingController');

// Import middleware
const {
  authenticate,
  authorize,
  requireDriverProfile,
  requireVerifiedDriver
} = require('../middleware/auth');

// Import validators
const {
  validate,
  validateQuery,
  locationUpdateValidation,
  nearbyDriversQueryValidation,
  locationHistoryQueryValidation
} = require('../validators/trackingValidator');

/**
 * @route   GET /api/v1/tracking/bookings/:bookingId
 * @desc    Get real-time booking tracking
 * @access  Private
 */
router.get('/bookings/:bookingId',
  authenticate,
  getBookingTracking
);

/**
 * @route   POST /api/v1/tracking/location
 * @desc    Update driver location
 * @access  Private (Verified Driver)
 */
router.post('/location',
  authenticate,
  authorize('driver'),
  requireVerifiedDriver,
  validate(locationUpdateValidation),
  updateDriverLocation
);

/**
 * @route   GET /api/v1/tracking/drivers/nearby
 * @desc    Get nearby drivers
 * @access  Private (User role)
 */
router.get('/drivers/nearby',
  authenticate,
  authorize('user'),
  validateQuery(nearbyDriversQueryValidation),
  getNearbyDrivers
);

/**
 * @route   GET /api/v1/tracking/bookings/:bookingId/history
 * @desc    Get driver location history for booking
 * @access  Private
 */
router.get('/bookings/:bookingId/history',
  authenticate,
  validateQuery(locationHistoryQueryValidation),
  getDriverLocationHistory
);

/**
 * @route   GET /api/v1/tracking/stats
 * @desc    Get live tracking statistics (Admin only)
 * @access  Private (Admin only)
 */
router.get('/stats',
  authenticate,
  authorize('admin'),
  getTrackingStats
);

module.exports = router;
