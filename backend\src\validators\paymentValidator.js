const Joi = require('joi');
const { PAYMENT_METHODS, PAYMENT_STATUS } = require('../config/constants');

// Create payment intent validation
const createPaymentIntentValidation = Joi.object({
  paymentMethod: Joi.string()
    .valid(...Object.values(PAYMENT_METHODS))
    .required()
    .messages({
      'any.only': 'Invalid payment method',
      'any.required': 'Payment method is required'
    })
});

// Confirm payment validation
const confirmPaymentValidation = Joi.object({
  paymentIntentId: Joi.string()
    .required()
    .trim()
    .min(10)
    .messages({
      'string.min': 'Invalid payment intent ID',
      'any.required': 'Payment intent ID is required'
    })
});

// Refund request validation
const refundRequestValidation = Joi.object({
  amount: Joi.number()
    .positive()
    .optional()
    .messages({
      'number.positive': 'Refund amount must be positive'
    }),
  reason: Joi.string()
    .required()
    .trim()
    .min(5)
    .max(500)
    .messages({
      'string.min': 'Refund reason must be at least 5 characters',
      'string.max': 'Refund reason cannot exceed 500 characters',
      'any.required': 'Refund reason is required'
    })
});

// Add payment method validation
const addPaymentMethodValidation = Joi.object({
  paymentMethodId: Joi.string()
    .required()
    .trim()
    .pattern(/^pm_[a-zA-Z0-9]+$/)
    .messages({
      'string.pattern.base': 'Invalid payment method ID format',
      'any.required': 'Payment method ID is required'
    }),
  setAsDefault: Joi.boolean()
    .default(false)
});

// Webhook validation
const webhookValidation = Joi.object({
  type: Joi.string()
    .required()
    .messages({
      'any.required': 'Webhook type is required'
    }),
  data: Joi.object()
    .required()
    .messages({
      'any.required': 'Webhook data is required'
    }),
  id: Joi.string()
    .required()
    .messages({
      'any.required': 'Webhook ID is required'
    })
});

// Driver payout request validation
const driverPayoutRequestValidation = Joi.object({
  amount: Joi.number()
    .positive()
    .required()
    .messages({
      'number.positive': 'Payout amount must be positive',
      'any.required': 'Payout amount is required'
    }),
  bankAccount: Joi.object({
    accountHolderName: Joi.string()
      .required()
      .trim()
      .min(2)
      .max(100)
      .messages({
        'string.min': 'Account holder name must be at least 2 characters',
        'string.max': 'Account holder name cannot exceed 100 characters',
        'any.required': 'Account holder name is required'
      }),
    accountNumber: Joi.string()
      .required()
      .trim()
      .min(8)
      .max(20)
      .pattern(/^\d+$/)
      .messages({
        'string.min': 'Account number must be at least 8 digits',
        'string.max': 'Account number cannot exceed 20 digits',
        'string.pattern.base': 'Account number must contain only digits',
        'any.required': 'Account number is required'
      }),
    routingNumber: Joi.string()
      .required()
      .trim()
      .length(9)
      .pattern(/^\d+$/)
      .messages({
        'string.length': 'Routing number must be exactly 9 digits',
        'string.pattern.base': 'Routing number must contain only digits',
        'any.required': 'Routing number is required'
      }),
    bankName: Joi.string()
      .required()
      .trim()
      .min(2)
      .max(100)
      .messages({
        'string.min': 'Bank name must be at least 2 characters',
        'string.max': 'Bank name cannot exceed 100 characters',
        'any.required': 'Bank name is required'
      })
    }).required()
});

// Payment query validation
const paymentQueryValidation = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10),
  status: Joi.string()
    .valid(...Object.values(PAYMENT_STATUS))
    .optional(),
  startDate: Joi.date()
    .optional(),
  endDate: Joi.date()
    .greater(Joi.ref('startDate'))
    .optional()
    .messages({
      'date.greater': 'End date must be after start date'
    }),
  search: Joi.string()
    .optional()
    .trim()
    .min(3)
    .max(50)
    .messages({
      'string.min': 'Search term must be at least 3 characters',
      'string.max': 'Search term cannot exceed 50 characters'
    })
});

// Payment analytics validation
const paymentAnalyticsValidation = Joi.object({
  startDate: Joi.date()
    .required()
    .messages({
      'any.required': 'Start date is required'
    }),
  endDate: Joi.date()
    .greater(Joi.ref('startDate'))
    .required()
    .messages({
      'date.greater': 'End date must be after start date',
      'any.required': 'End date is required'
    }),
  groupBy: Joi.string()
    .valid('day', 'week', 'month')
    .default('day'),
  paymentMethod: Joi.string()
    .valid(...Object.values(PAYMENT_METHODS))
    .optional()
});

// Dispute validation
const disputeValidation = Joi.object({
  reason: Joi.string()
    .valid('duplicate', 'fraudulent', 'subscription_canceled', 'product_unacceptable', 'product_not_received', 'unrecognized', 'credit_not_processed', 'general')
    .required()
    .messages({
      'any.only': 'Invalid dispute reason',
      'any.required': 'Dispute reason is required'
    }),
  evidence: Joi.object({
    customerCommunication: Joi.string().optional(),
    receipt: Joi.string().optional(),
    shippingDocumentation: Joi.string().optional(),
    duplicateChargeDocumentation: Joi.string().optional(),
    refundPolicy: Joi.string().optional(),
    cancellationPolicy: Joi.string().optional(),
    customerSignature: Joi.string().optional(),
    uncategorizedFile: Joi.string().optional(),
    serviceDate: Joi.date().optional(),
    serviceDocumentation: Joi.string().optional(),
    uncategorizedText: Joi.string().max(1000).optional()
  }).optional()
});

// Subscription validation (for future use)
const subscriptionValidation = Joi.object({
  priceId: Joi.string()
    .required()
    .trim()
    .pattern(/^price_[a-zA-Z0-9]+$/)
    .messages({
      'string.pattern.base': 'Invalid price ID format',
      'any.required': 'Price ID is required'
    }),
  paymentMethodId: Joi.string()
    .required()
    .trim()
    .pattern(/^pm_[a-zA-Z0-9]+$/)
    .messages({
      'string.pattern.base': 'Invalid payment method ID format',
      'any.required': 'Payment method ID is required'
    }),
  couponId: Joi.string()
    .optional()
    .trim()
    .pattern(/^[a-zA-Z0-9_-]+$/)
    .messages({
      'string.pattern.base': 'Invalid coupon ID format'
    })
});

// Invoice validation
const invoiceValidation = Joi.object({
  customerId: Joi.string()
    .required()
    .trim()
    .pattern(/^cus_[a-zA-Z0-9]+$/)
    .messages({
      'string.pattern.base': 'Invalid customer ID format',
      'any.required': 'Customer ID is required'
    }),
  items: Joi.array()
    .items(
      Joi.object({
        description: Joi.string().required(),
        amount: Joi.number().positive().required(),
        quantity: Joi.number().integer().positive().default(1)
      })
    )
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one item is required',
      'any.required': 'Items are required'
    }),
  dueDate: Joi.date()
    .greater('now')
    .optional()
    .messages({
      'date.greater': 'Due date must be in the future'
    }),
  metadata: Joi.object().optional()
});

// Validation middleware
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    req.body = value;
    next();
  };
};

// Query validation middleware
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Query validation failed',
        details: errors
      });
    }

    req.query = value;
    next();
  };
};

module.exports = {
  createPaymentIntentValidation,
  confirmPaymentValidation,
  refundRequestValidation,
  addPaymentMethodValidation,
  webhookValidation,
  driverPayoutRequestValidation,
  paymentQueryValidation,
  paymentAnalyticsValidation,
  disputeValidation,
  subscriptionValidation,
  invoiceValidation,
  validate,
  validateQuery
};
