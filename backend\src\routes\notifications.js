const express = require('express');
const router = express.Router();

// Import controllers
const {
  getUserNotifications,
  getNotificationDetails,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  updateNotificationPreferences,
  getNotificationPreferences,
  sendTestNotification,
  sendBulkNotification,
  getNotificationStats
} = require('../controllers/notificationController');

// Import middleware
const {
  authenticate,
  authorize
} = require('../middleware/auth');

// Import validators
const {
  validate,
  validateQuery,
  notificationPreferencesValidation,
  testNotificationValidation,
  bulkNotificationValidation,
  notificationQueryValidation,
  notificationStatsQueryValidation
} = require('../validators/notificationValidator');

/**
 * @route   GET /api/v1/notifications
 * @desc    Get user notifications
 * @access  Private
 */
router.get('/',
  authenticate,
  validateQuery(notificationQueryValidation),
  getUserNotifications
);

/**
 * @route   GET /api/v1/notifications/:notificationId
 * @desc    Get notification details
 * @access  Private
 */
router.get('/:notificationId',
  authenticate,
  getNotificationDetails
);

/**
 * @route   PUT /api/v1/notifications/:notificationId/read
 * @desc    Mark notification as read
 * @access  Private
 */
router.put('/:notificationId/read',
  authenticate,
  markAsRead
);

/**
 * @route   PUT /api/v1/notifications/read-all
 * @desc    Mark all notifications as read
 * @access  Private
 */
router.put('/read-all',
  authenticate,
  markAllAsRead
);

/**
 * @route   DELETE /api/v1/notifications/:notificationId
 * @desc    Delete notification
 * @access  Private
 */
router.delete('/:notificationId',
  authenticate,
  deleteNotification
);

/**
 * @route   GET /api/v1/notifications/preferences
 * @desc    Get notification preferences
 * @access  Private
 */
router.get('/preferences',
  authenticate,
  getNotificationPreferences
);

/**
 * @route   PUT /api/v1/notifications/preferences
 * @desc    Update notification preferences
 * @access  Private
 */
router.put('/preferences',
  authenticate,
  validate(notificationPreferencesValidation),
  updateNotificationPreferences
);

/**
 * @route   POST /api/v1/notifications/test
 * @desc    Send test notification (Admin only)
 * @access  Private (Admin only)
 */
router.post('/test',
  authenticate,
  authorize('admin'),
  validate(testNotificationValidation),
  sendTestNotification
);

/**
 * @route   POST /api/v1/notifications/bulk
 * @desc    Send bulk notification (Admin only)
 * @access  Private (Admin only)
 */
router.post('/bulk',
  authenticate,
  authorize('admin'),
  validate(bulkNotificationValidation),
  sendBulkNotification
);

/**
 * @route   GET /api/v1/notifications/stats
 * @desc    Get notification statistics (Admin only)
 * @access  Private (Admin only)
 */
router.get('/stats',
  authenticate,
  authorize('admin'),
  validateQuery(notificationStatsQueryValidation),
  getNotificationStats
);

module.exports = router;
