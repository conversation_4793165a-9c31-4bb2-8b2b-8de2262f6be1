const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const logger = require('./logger');

/**
 * Password security configuration
 */
const PASSWORD_CONFIG = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  saltRounds: 12,
  maxAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
  passwordHistory: 5 // Remember last 5 passwords
};

/**
 * JWT configuration
 */
const JWT_CONFIG = {
  accessTokenExpiry: '15m',
  refreshTokenExpiry: '7d',
  algorithm: 'HS256',
  issuer: 'parter-logistics',
  audience: 'parter-users'
};

/**
 * Session configuration
 */
const SESSION_CONFIG = {
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  secure: process.env.NODE_ENV === 'production',
  httpOnly: true,
  sameSite: 'strict'
};

/**
 * Encryption utilities
 */
class EncryptionService {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
    this.secretKey = process.env.ENCRYPTION_KEY || this.generateKey();
  }

  generateKey() {
    return crypto.randomBytes(this.keyLength);
  }

  encrypt(text) {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipher(this.algorithm, this.secretKey, iv);
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      };
    } catch (error) {
      logger.error('Encryption error:', error);
      throw new Error('Encryption failed');
    }
  }

  decrypt(encryptedData) {
    try {
      const { encrypted, iv, tag } = encryptedData;
      const decipher = crypto.createDecipher(this.algorithm, this.secretKey, Buffer.from(iv, 'hex'));
      
      decipher.setAuthTag(Buffer.from(tag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error('Decryption error:', error);
      throw new Error('Decryption failed');
    }
  }

  hash(data) {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }
}

/**
 * Password utilities
 */
class PasswordService {
  static validatePassword(password) {
    const errors = [];
    
    if (password.length < PASSWORD_CONFIG.minLength) {
      errors.push(`Password must be at least ${PASSWORD_CONFIG.minLength} characters long`);
    }
    
    if (password.length > PASSWORD_CONFIG.maxLength) {
      errors.push(`Password cannot exceed ${PASSWORD_CONFIG.maxLength} characters`);
    }
    
    if (PASSWORD_CONFIG.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (PASSWORD_CONFIG.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (PASSWORD_CONFIG.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (PASSWORD_CONFIG.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    // Check for common patterns
    if (/(.)\1{2,}/.test(password)) {
      errors.push('Password cannot contain repeated characters');
    }
    
    if (/123|abc|qwe|password|admin/i.test(password)) {
      errors.push('Password cannot contain common patterns');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static async hashPassword(password) {
    try {
      const salt = await bcrypt.genSalt(PASSWORD_CONFIG.saltRounds);
      return await bcrypt.hash(password, salt);
    } catch (error) {
      logger.error('Password hashing error:', error);
      throw new Error('Password hashing failed');
    }
  }

  static async comparePassword(password, hashedPassword) {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      logger.error('Password comparison error:', error);
      throw new Error('Password comparison failed');
    }
  }

  static generateSecurePassword(length = 16) {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = uppercase + lowercase + numbers + symbols;
    let password = '';
    
    // Ensure at least one character from each category
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }
}

/**
 * Two-Factor Authentication Service
 */
class TwoFactorService {
  static generateSecret(userEmail) {
    return speakeasy.generateSecret({
      name: `Parter Logistics (${userEmail})`,
      issuer: 'Parter Logistics',
      length: 32
    });
  }

  static async generateQRCode(secret) {
    try {
      return await QRCode.toDataURL(secret.otpauth_url);
    } catch (error) {
      logger.error('QR code generation error:', error);
      throw new Error('QR code generation failed');
    }
  }

  static verifyToken(token, secret) {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2 // Allow 2 time steps (60 seconds) of variance
    });
  }

  static generateBackupCodes(count = 10) {
    const codes = [];
    for (let i = 0; i < count; i++) {
      codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }
    return codes;
  }
}

/**
 * JWT Service
 */
class JWTService {
  static generateTokens(payload) {
    const accessToken = jwt.sign(
      payload,
      process.env.JWT_SECRET,
      {
        expiresIn: JWT_CONFIG.accessTokenExpiry,
        algorithm: JWT_CONFIG.algorithm,
        issuer: JWT_CONFIG.issuer,
        audience: JWT_CONFIG.audience
      }
    );

    const refreshToken = jwt.sign(
      { userId: payload.userId },
      process.env.JWT_REFRESH_SECRET,
      {
        expiresIn: JWT_CONFIG.refreshTokenExpiry,
        algorithm: JWT_CONFIG.algorithm,
        issuer: JWT_CONFIG.issuer,
        audience: JWT_CONFIG.audience
      }
    );

    return { accessToken, refreshToken };
  }

  static verifyAccessToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET, {
        algorithm: JWT_CONFIG.algorithm,
        issuer: JWT_CONFIG.issuer,
        audience: JWT_CONFIG.audience
      });
    } catch (error) {
      throw new Error('Invalid access token');
    }
  }

  static verifyRefreshToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_REFRESH_SECRET, {
        algorithm: JWT_CONFIG.algorithm,
        issuer: JWT_CONFIG.issuer,
        audience: JWT_CONFIG.audience
      });
    } catch (error) {
      throw new Error('Invalid refresh token');
    }
  }

  static decodeToken(token) {
    return jwt.decode(token);
  }
}

/**
 * API Key Service
 */
class APIKeyService {
  static generateAPIKey() {
    const prefix = 'pk_';
    const key = crypto.randomBytes(32).toString('hex');
    return prefix + key;
  }

  static hashAPIKey(apiKey) {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }

  static validateAPIKey(apiKey) {
    return /^pk_[a-f0-9]{64}$/.test(apiKey);
  }
}

/**
 * Security audit logging
 */
class SecurityAuditLogger {
  static logSecurityEvent(event, details = {}) {
    logger.warn('Security Event', {
      event,
      timestamp: new Date().toISOString(),
      ...details
    });
  }

  static logFailedLogin(email, ip, userAgent) {
    this.logSecurityEvent('FAILED_LOGIN', {
      email,
      ip,
      userAgent
    });
  }

  static logSuccessfulLogin(userId, email, ip, userAgent) {
    this.logSecurityEvent('SUCCESSFUL_LOGIN', {
      userId,
      email,
      ip,
      userAgent
    });
  }

  static logPasswordChange(userId, ip) {
    this.logSecurityEvent('PASSWORD_CHANGE', {
      userId,
      ip
    });
  }

  static logSuspiciousActivity(type, details) {
    this.logSecurityEvent('SUSPICIOUS_ACTIVITY', {
      type,
      ...details
    });
  }

  static logDataAccess(userId, resource, action, ip) {
    this.logSecurityEvent('DATA_ACCESS', {
      userId,
      resource,
      action,
      ip
    });
  }
}

module.exports = {
  PASSWORD_CONFIG,
  JWT_CONFIG,
  SESSION_CONFIG,
  EncryptionService,
  PasswordService,
  TwoFactorService,
  JWTService,
  APIKeyService,
  SecurityAuditLogger
};
