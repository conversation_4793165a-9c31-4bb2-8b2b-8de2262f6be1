const { Booking, Driver } = require('../models');
const { RESPONSE_MESSAGES, BOOKING_STATUS } = require('../config/constants');
const { calculateDistance, calculateETA, getRoute } = require('../services/mapsService');
const socketService = require('../services/socketService');
const logger = require('../config/logger');

/**
 * Get real-time booking tracking
 */
const getBookingTracking = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;

    // Find booking
    let query = { bookingId };
    
    // Check permissions
    if (userRole === 'user') {
      query.user = userId;
    } else if (userRole === 'driver') {
      query.driver = req.driver._id;
    }
    // Admins can see all bookings

    const booking = await Booking.findOne(query)
      .populate('user', 'firstName lastName phone')
      .populate({
        path: 'driver',
        populate: {
          path: 'user',
          select: 'firstName lastName phone profilePicture'
        }
      });

    if (!booking) {
      return res.status(404).json({
        success: false,
        error: 'Booking not found'
      });
    }

    // Get driver's current location if available
    let driverLocation = null;
    let estimatedArrival = null;
    let route = null;

    if (booking.driver) {
      const driver = await Driver.findById(booking.driver._id);
      if (driver && driver.currentLocation) {
        driverLocation = {
          latitude: driver.currentLocation.coordinates[1],
          longitude: driver.currentLocation.coordinates[0],
          lastUpdate: driver.lastLocationUpdate,
          heading: driver.locationData?.heading,
          speed: driver.locationData?.speed
        };

        // Calculate ETA to pickup or dropoff based on booking status
        let destination = booking.pickupLocation;
        if ([BOOKING_STATUS.PICKUP_COMPLETED, BOOKING_STATUS.IN_TRANSIT].includes(booking.status)) {
          destination = booking.dropoffLocations[0]; // Use first dropoff for now
        }

        try {
          estimatedArrival = await calculateETA(
            { coordinates: driver.currentLocation },
            destination
          );

          // Get route if booking is active
          if ([BOOKING_STATUS.DRIVER_ASSIGNED, BOOKING_STATUS.DRIVER_ARRIVED, BOOKING_STATUS.PICKUP_COMPLETED, BOOKING_STATUS.IN_TRANSIT].includes(booking.status)) {
            route = await getRoute(
              { coordinates: driver.currentLocation },
              destination
            );
          }
        } catch (error) {
          logger.warn('Failed to calculate ETA or route:', error);
        }
      }
    }

    // Get tracking history
    const trackingHistory = booking.tracking.sort((a, b) => b.timestamp - a.timestamp);

    res.json({
      success: true,
      data: {
        booking: {
          bookingId: booking.bookingId,
          status: booking.status,
          vehicleType: booking.vehicleType,
          pickupLocation: booking.pickupLocation,
          dropoffLocations: booking.dropoffLocations,
          createdAt: booking.createdAt,
          scheduledDateTime: booking.scheduledDateTime,
          driverAssignedAt: booking.driverAssignedAt,
          tripStartedAt: booking.tripStartedAt,
          tripCompletedAt: booking.tripCompletedAt
        },
        driver: booking.driver ? {
          id: booking.driver._id,
          name: `${booking.driver.user.firstName} ${booking.driver.user.lastName}`,
          phone: booking.driver.user.phone,
          profilePicture: booking.driver.user.profilePicture,
          rating: booking.driver.ratings?.average,
          vehicle: booking.driver.activeVehicle
        } : null,
        tracking: {
          currentLocation: driverLocation,
          estimatedArrival,
          route,
          history: trackingHistory
        },
        realTimeEnabled: socketService.isDriverOnline(booking.driver?._id)
      }
    });
  } catch (error) {
    logger.error('Get booking tracking error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Update driver location (for drivers)
 */
const updateDriverLocation = async (req, res) => {
  try {
    const { latitude, longitude, heading, speed, accuracy } = req.body;
    const driverId = req.driver._id;

    // Update driver location
    const driver = await Driver.findByIdAndUpdate(
      driverId,
      {
        currentLocation: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        lastLocationUpdate: new Date(),
        'locationData.heading': heading,
        'locationData.speed': speed,
        'locationData.accuracy': accuracy
      },
      { new: true }
    );

    if (!driver) {
      return res.status(404).json({
        success: false,
        error: 'Driver not found'
      });
    }

    // Find active bookings for this driver
    const activeBookings = await Booking.find({
      driver: driverId,
      status: { $in: [BOOKING_STATUS.DRIVER_ASSIGNED, BOOKING_STATUS.DRIVER_ARRIVED, BOOKING_STATUS.PICKUP_COMPLETED, BOOKING_STATUS.IN_TRANSIT] }
    });

    // Update tracking for each active booking
    for (const booking of activeBookings) {
      // Add location to tracking history
      booking.tracking.push({
        status: booking.status,
        timestamp: new Date(),
        location: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        notes: 'Location update',
        metadata: {
          heading,
          speed,
          accuracy
        }
      });

      await booking.save();

      // Broadcast location update via WebSocket
      socketService.broadcastBookingUpdate(booking.bookingId, {
        type: 'location_update',
        location: { latitude, longitude },
        heading,
        speed,
        accuracy,
        driverId
      });
    }

    logger.info(`Location updated for driver: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Location updated successfully',
      data: {
        location: { latitude, longitude },
        activeBookings: activeBookings.length
      }
    });
  } catch (error) {
    logger.error('Update driver location error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get nearby drivers for user
 */
const getNearbyDrivers = async (req, res) => {
  try {
    const { latitude, longitude, vehicleType, radius = 10 } = req.query;

    if (!latitude || !longitude) {
      return res.status(400).json({
        success: false,
        error: 'Latitude and longitude are required'
      });
    }

    // Find nearby available drivers
    const drivers = await Driver.find({
      status: 'online',
      verificationStatus: 'approved',
      'vehicles.type': vehicleType,
      'vehicles.isActive': true,
      currentLocation: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(longitude), parseFloat(latitude)]
          },
          $maxDistance: radius * 1000 // Convert km to meters
        }
      }
    })
    .populate('user', 'firstName lastName profilePicture')
    .limit(20)
    .select('user currentLocation ratings vehicles status lastLocationUpdate');

    // Calculate distance and add real-time status
    const driversWithDistance = drivers.map(driver => {
      const distance = calculateDistance(
        parseFloat(latitude), parseFloat(longitude),
        driver.currentLocation.coordinates[1], driver.currentLocation.coordinates[0]
      );

      return {
        id: driver._id,
        name: `${driver.user.firstName} ${driver.user.lastName}`,
        profilePicture: driver.user.profilePicture,
        location: {
          latitude: driver.currentLocation.coordinates[1],
          longitude: driver.currentLocation.coordinates[0]
        },
        distance: Math.round(distance * 100) / 100,
        rating: driver.ratings?.average || 0,
        vehicle: driver.vehicles.find(v => v.type === vehicleType && v.isActive),
        isOnline: socketService.isDriverOnline(driver._id),
        lastLocationUpdate: driver.lastLocationUpdate
      };
    });

    // Sort by distance
    driversWithDistance.sort((a, b) => a.distance - b.distance);

    res.json({
      success: true,
      data: {
        drivers: driversWithDistance,
        searchLocation: { latitude: parseFloat(latitude), longitude: parseFloat(longitude) },
        radius,
        totalFound: driversWithDistance.length
      }
    });
  } catch (error) {
    logger.error('Get nearby drivers error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get driver's location history
 */
const getDriverLocationHistory = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { startTime, endTime } = req.query;
    const userId = req.user._id;
    const userRole = req.user.role;

    // Find booking and check permissions
    let query = { bookingId };
    if (userRole === 'user') {
      query.user = userId;
    } else if (userRole === 'driver') {
      query.driver = req.driver._id;
    }

    const booking = await Booking.findOne(query);
    if (!booking) {
      return res.status(404).json({
        success: false,
        error: 'Booking not found'
      });
    }

    // Filter tracking history by time range if provided
    let trackingHistory = booking.tracking.filter(track => track.location);

    if (startTime) {
      trackingHistory = trackingHistory.filter(track => 
        track.timestamp >= new Date(startTime)
      );
    }

    if (endTime) {
      trackingHistory = trackingHistory.filter(track => 
        track.timestamp <= new Date(endTime)
      );
    }

    // Sort by timestamp
    trackingHistory.sort((a, b) => a.timestamp - b.timestamp);

    // Format for response
    const locationHistory = trackingHistory.map(track => ({
      timestamp: track.timestamp,
      location: {
        latitude: track.location.coordinates[1],
        longitude: track.location.coordinates[0]
      },
      status: track.status,
      heading: track.metadata?.heading,
      speed: track.metadata?.speed,
      accuracy: track.metadata?.accuracy
    }));

    res.json({
      success: true,
      data: {
        bookingId,
        locationHistory,
        totalPoints: locationHistory.length,
        timeRange: {
          start: startTime || (locationHistory[0]?.timestamp),
          end: endTime || (locationHistory[locationHistory.length - 1]?.timestamp)
        }
      }
    });
  } catch (error) {
    logger.error('Get driver location history error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get live tracking statistics
 */
const getTrackingStats = async (req, res) => {
  try {
    const stats = socketService.getConnectedUsersCount();
    
    // Get additional stats
    const [activeBookings, onlineDrivers] = await Promise.all([
      Booking.countDocuments({
        status: { $in: [BOOKING_STATUS.DRIVER_ASSIGNED, BOOKING_STATUS.DRIVER_ARRIVED, BOOKING_STATUS.PICKUP_COMPLETED, BOOKING_STATUS.IN_TRANSIT] }
      }),
      Driver.countDocuments({ status: 'online' })
    ]);

    res.json({
      success: true,
      data: {
        ...stats,
        activeBookings,
        onlineDrivers,
        timestamp: new Date()
      }
    });
  } catch (error) {
    logger.error('Get tracking stats error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

module.exports = {
  getBookingTracking,
  updateDriverLocation,
  getNearbyDrivers,
  getDriverLocationHistory,
  getTrackingStats
};
