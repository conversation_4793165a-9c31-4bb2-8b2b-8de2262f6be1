{"name": "base32.js", "version": "0.0.1", "author": "<PERSON>-<PERSON> <<EMAIL>>", "description": "Base 32 encodings for JavaScript", "keywords": ["base32", "crockford", "rfc4648", "encoding", "decoding", "crc"], "license": "MIT", "main": "index.js", "browser": "base32.js", "repository": {"type": "git", "url": "git://github.com/mikepb/base32.js.git"}, "scripts": {"test": "mocha --reporter dot", "karma": "karma start --single-run", "dist": "webpack base32.js dist/base32.js && webpack --optimize-minimize base32.js dist/base32.min.js", "doc": "jsdoc -c jsdoc.json"}, "dependencies": {}, "devDependencies": {"jsdoc": "*", "karma": "*", "karma-chrome-launcher": "*", "karma-mocha": "*", "karma-webpack": "*", "mocha": "*", "webpack": "*"}}