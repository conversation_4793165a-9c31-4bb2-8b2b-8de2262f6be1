const Joi = require('joi');
const { VEHICLE_TYPES } = require('../config/constants');

// Location update validation
const locationUpdateValidation = Joi.object({
  latitude: Joi.number()
    .min(-90)
    .max(90)
    .required()
    .messages({
      'number.min': 'Latitude must be between -90 and 90',
      'number.max': 'Latitude must be between -90 and 90',
      'any.required': 'Latitude is required'
    }),
  longitude: Joi.number()
    .min(-180)
    .max(180)
    .required()
    .messages({
      'number.min': 'Longitude must be between -180 and 180',
      'number.max': 'Longitude must be between -180 and 180',
      'any.required': 'Longitude is required'
    }),
  heading: Joi.number()
    .min(0)
    .max(360)
    .optional()
    .messages({
      'number.min': 'Heading must be between 0 and 360',
      'number.max': 'Heading must be between 0 and 360'
    }),
  speed: Joi.number()
    .min(0)
    .max(200)
    .optional()
    .messages({
      'number.min': 'Speed cannot be negative',
      'number.max': 'Speed cannot exceed 200 km/h'
    }),
  accuracy: Joi.number()
    .positive()
    .optional()
    .messages({
      'number.positive': 'Accuracy must be positive'
    })
});

// Nearby drivers query validation
const nearbyDriversQueryValidation = Joi.object({
  latitude: Joi.number()
    .min(-90)
    .max(90)
    .required()
    .messages({
      'number.min': 'Latitude must be between -90 and 90',
      'number.max': 'Latitude must be between -90 and 90',
      'any.required': 'Latitude is required'
    }),
  longitude: Joi.number()
    .min(-180)
    .max(180)
    .required()
    .messages({
      'number.min': 'Longitude must be between -180 and 180',
      'number.max': 'Longitude must be between -180 and 180',
      'any.required': 'Longitude is required'
    }),
  vehicleType: Joi.string()
    .valid(...Object.values(VEHICLE_TYPES))
    .optional()
    .messages({
      'any.only': 'Invalid vehicle type'
    }),
  radius: Joi.number()
    .positive()
    .max(50)
    .default(10)
    .messages({
      'number.positive': 'Radius must be positive',
      'number.max': 'Radius cannot exceed 50 km'
    })
});

// Location history query validation
const locationHistoryQueryValidation = Joi.object({
  startTime: Joi.date()
    .optional()
    .messages({
      'date.base': 'Start time must be a valid date'
    }),
  endTime: Joi.date()
    .greater(Joi.ref('startTime'))
    .optional()
    .messages({
      'date.greater': 'End time must be after start time',
      'date.base': 'End time must be a valid date'
    }),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(1000)
    .default(100)
    .messages({
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 1000'
    })
});

// Geofence validation
const geofenceValidation = Joi.object({
  name: Joi.string()
    .required()
    .trim()
    .min(2)
    .max(100)
    .messages({
      'string.min': 'Geofence name must be at least 2 characters',
      'string.max': 'Geofence name cannot exceed 100 characters',
      'any.required': 'Geofence name is required'
    }),
  type: Joi.string()
    .valid('circle', 'polygon')
    .required()
    .messages({
      'any.only': 'Geofence type must be circle or polygon',
      'any.required': 'Geofence type is required'
    }),
  coordinates: Joi.when('type', {
    is: 'circle',
    then: Joi.object({
      center: Joi.object({
        latitude: Joi.number().min(-90).max(90).required(),
        longitude: Joi.number().min(-180).max(180).required()
      }).required(),
      radius: Joi.number().positive().max(50000).required() // Max 50km radius
    }).required(),
    otherwise: Joi.object({
      points: Joi.array()
        .items(
          Joi.object({
            latitude: Joi.number().min(-90).max(90).required(),
            longitude: Joi.number().min(-180).max(180).required()
          })
        )
        .min(3)
        .max(100)
        .required()
        .messages({
          'array.min': 'Polygon must have at least 3 points',
          'array.max': 'Polygon cannot have more than 100 points'
        })
    }).required()
  }),
  isActive: Joi.boolean()
    .default(true),
  description: Joi.string()
    .optional()
    .trim()
    .max(500)
    .messages({
      'string.max': 'Description cannot exceed 500 characters'
    })
});

// Route optimization validation
const routeOptimizationValidation = Joi.object({
  origin: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required()
  }).required(),
  destinations: Joi.array()
    .items(
      Joi.object({
        latitude: Joi.number().min(-90).max(90).required(),
        longitude: Joi.number().min(-180).max(180).required(),
        priority: Joi.number().integer().min(1).max(10).default(5),
        timeWindow: Joi.object({
          start: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
          end: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional()
        }).optional()
      })
    )
    .min(1)
    .max(25)
    .required()
    .messages({
      'array.min': 'At least one destination is required',
      'array.max': 'Maximum 25 destinations allowed'
    }),
  vehicleType: Joi.string()
    .valid(...Object.values(VEHICLE_TYPES))
    .required(),
  optimizeFor: Joi.string()
    .valid('time', 'distance', 'fuel')
    .default('time')
});

// ETA calculation validation
const etaCalculationValidation = Joi.object({
  origin: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required()
  }).required(),
  destination: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required()
  }).required(),
  vehicleType: Joi.string()
    .valid(...Object.values(VEHICLE_TYPES))
    .optional(),
  departureTime: Joi.date()
    .optional()
    .messages({
      'date.base': 'Departure time must be a valid date'
    }),
  trafficModel: Joi.string()
    .valid('best_guess', 'pessimistic', 'optimistic')
    .default('best_guess')
});

// Distance calculation validation
const distanceCalculationValidation = Joi.object({
  origins: Joi.array()
    .items(
      Joi.object({
        latitude: Joi.number().min(-90).max(90).required(),
        longitude: Joi.number().min(-180).max(180).required()
      })
    )
    .min(1)
    .max(25)
    .required()
    .messages({
      'array.min': 'At least one origin is required',
      'array.max': 'Maximum 25 origins allowed'
    }),
  destinations: Joi.array()
    .items(
      Joi.object({
        latitude: Joi.number().min(-90).max(90).required(),
        longitude: Joi.number().min(-180).max(180).required()
      })
    )
    .min(1)
    .max(25)
    .required()
    .messages({
      'array.min': 'At least one destination is required',
      'array.max': 'Maximum 25 destinations allowed'
    }),
  units: Joi.string()
    .valid('metric', 'imperial')
    .default('metric'),
  mode: Joi.string()
    .valid('driving', 'walking', 'bicycling', 'transit')
    .default('driving')
});

// Tracking alert validation
const trackingAlertValidation = Joi.object({
  bookingId: Joi.string()
    .required()
    .trim()
    .messages({
      'any.required': 'Booking ID is required'
    }),
  alertType: Joi.string()
    .valid('delay', 'route_deviation', 'speed_limit', 'geofence_exit', 'geofence_enter', 'emergency')
    .required()
    .messages({
      'any.only': 'Invalid alert type',
      'any.required': 'Alert type is required'
    }),
  severity: Joi.string()
    .valid('low', 'medium', 'high', 'critical')
    .default('medium'),
  message: Joi.string()
    .required()
    .trim()
    .min(5)
    .max(500)
    .messages({
      'string.min': 'Alert message must be at least 5 characters',
      'string.max': 'Alert message cannot exceed 500 characters',
      'any.required': 'Alert message is required'
    }),
  location: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required()
  }).optional(),
  metadata: Joi.object().optional()
});

// Validation middleware
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    req.body = value;
    next();
  };
};

// Query validation middleware
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Query validation failed',
        details: errors
      });
    }

    req.query = value;
    next();
  };
};

module.exports = {
  locationUpdateValidation,
  nearbyDriversQueryValidation,
  locationHistoryQueryValidation,
  geofenceValidation,
  routeOptimizationValidation,
  etaCalculationValidation,
  distanceCalculationValidation,
  trackingAlertValidation,
  validate,
  validateQuery
};
