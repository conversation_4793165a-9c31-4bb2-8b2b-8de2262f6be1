const { Payment, Booking, User } = require('../models');
const { RESPONSE_MESSAGES, PAYMENT_STATUS, PAGINATION } = require('../config/constants');
const {
  createPaymentIntent,
  processPayment,
  confirmPayment,
  processRefund,
  createCustomer,
  addPaymentMethod,
  getCustomerPaymentMethods,
  removePaymentMethod,
  processDriverPayout
} = require('../services/paymentService');
const logger = require('../config/logger');

/**
 * Create payment intent for booking
 */
const createBookingPaymentIntent = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { paymentMethod } = req.body;
    const userId = req.user._id;

    // Find booking
    const booking = await Booking.findOne({ bookingId, user: userId });
    if (!booking) {
      return res.status(404).json({
        success: false,
        error: 'Booking not found'
      });
    }

    // Check if payment already exists
    const existingPayment = await Payment.findOne({ booking: booking._id });
    if (existingPayment && existingPayment.status === PAYMENT_STATUS.COMPLETED) {
      return res.status(400).json({
        success: false,
        error: 'Payment already completed for this booking'
      });
    }

    // Get or create Stripe customer
    let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;
    if (!stripeCustomerId) {
      const customer = await createCustomer({
        userId,
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        phone: req.user.phone
      });
      stripeCustomerId = customer.id;
    }

    // Create payment intent
    const paymentIntent = await createPaymentIntent({
      bookingId,
      amount: booking.fareBreakdown.totalFare,
      paymentMethod,
      customerId: stripeCustomerId,
      metadata: {
        userId: userId.toString(),
        driverId: booking.driver?.toString()
      }
    });

    logger.info(`Payment intent created for booking: ${bookingId} by user: ${req.user.email}`);

    res.json({
      success: true,
      data: paymentIntent
    });
  } catch (error) {
    logger.error('Create booking payment intent error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Confirm payment for booking
 */
const confirmBookingPayment = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { paymentIntentId } = req.body;
    const userId = req.user._id;

    // Find booking
    const booking = await Booking.findOne({ bookingId, user: userId });
    if (!booking) {
      return res.status(404).json({
        success: false,
        error: 'Booking not found'
      });
    }

    // Process payment if not already processed
    let payment = await Payment.findOne({ booking: booking._id });
    if (!payment) {
      const { calculateDriverEarnings } = require('../services/fareService');
      const driverEarnings = calculateDriverEarnings(booking.fareBreakdown);

      payment = await processPayment({
        bookingId,
        userId,
        driverId: booking.driver,
        amount: booking.fareBreakdown.totalFare,
        paymentMethod: booking.paymentMethod,
        paymentIntentId,
        breakdown: {
          subtotal: booking.fareBreakdown.totalFare - booking.fareBreakdown.tax,
          tax: booking.fareBreakdown.tax,
          tips: booking.fareBreakdown.tips,
          discount: booking.fareBreakdown.discount,
          platformFee: booking.fareBreakdown.platformFee || 0,
          driverEarnings: driverEarnings.netEarnings
        }
      });
    }

    // Confirm payment
    const result = await confirmPayment(paymentIntentId);

    logger.info(`Payment confirmed for booking: ${bookingId} by user: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Payment confirmed successfully',
      data: result
    });
  } catch (error) {
    logger.error('Confirm booking payment error:', error);
    res.status(500).json({
      success: false,
      error: error.message || RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get user payments
 */
const getUserPayments = async (req, res) => {
  try {
    const userId = req.user._id;
    const {
      page = PAGINATION.DEFAULT_PAGE,
      limit = PAGINATION.DEFAULT_LIMIT,
      status,
      startDate,
      endDate
    } = req.query;

    const query = { user: userId };
    
    if (status) query.status = status;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    const payments = await Payment.find(query)
      .populate('booking', 'bookingId vehicleType pickupLocation dropoffLocations')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Payment.countDocuments(query);

    res.json({
      success: true,
      data: {
        payments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get user payments error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get payment details
 */
const getPaymentDetails = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;

    let query = { paymentId };
    
    // Users can only see their own payments
    if (userRole === 'user') {
      query.user = userId;
    } else if (userRole === 'driver') {
      query.driver = req.driver._id;
    }
    // Admins can see all payments

    const payment = await Payment.findOne(query)
      .populate('booking', 'bookingId vehicleType pickupLocation dropoffLocations')
      .populate('user', 'firstName lastName email')
      .populate({
        path: 'driver',
        populate: {
          path: 'user',
          select: 'firstName lastName email'
        }
      });

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    res.json({
      success: true,
      data: { payment }
    });
  } catch (error) {
    logger.error('Get payment details error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Request refund
 */
const requestRefund = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const { amount, reason } = req.body;
    const userId = req.user._id;

    // Find payment
    const payment = await Payment.findOne({ paymentId, user: userId });
    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    if (payment.status !== PAYMENT_STATUS.COMPLETED) {
      return res.status(400).json({
        success: false,
        error: 'Can only refund completed payments'
      });
    }

    // Process refund
    const refund = await processRefund({
      paymentId,
      amount,
      reason,
      requestedBy: userId
    });

    logger.info(`Refund requested for payment: ${paymentId} by user: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Refund processed successfully',
      data: refund
    });
  } catch (error) {
    logger.error('Request refund error:', error);
    res.status(500).json({
      success: false,
      error: error.message || RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Add payment method
 */
const addUserPaymentMethod = async (req, res) => {
  try {
    const { paymentMethodId, setAsDefault } = req.body;
    const userId = req.user._id;

    // Get or create Stripe customer
    let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;
    if (!stripeCustomerId) {
      const customer = await createCustomer({
        userId,
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        phone: req.user.phone
      });
      stripeCustomerId = customer.id;
    }

    // Add payment method
    const paymentMethod = await addPaymentMethod({
      customerId: stripeCustomerId,
      paymentMethodId,
      setAsDefault
    });

    logger.info(`Payment method added for user: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Payment method added successfully',
      data: { paymentMethod }
    });
  } catch (error) {
    logger.error('Add payment method error:', error);
    res.status(500).json({
      success: false,
      error: error.message || RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get user payment methods
 */
const getUserPaymentMethods = async (req, res) => {
  try {
    const stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;
    
    if (!stripeCustomerId) {
      return res.json({
        success: true,
        data: { paymentMethods: [] }
      });
    }

    const paymentMethods = await getCustomerPaymentMethods(stripeCustomerId);

    res.json({
      success: true,
      data: { paymentMethods }
    });
  } catch (error) {
    logger.error('Get user payment methods error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Remove payment method
 */
const removeUserPaymentMethod = async (req, res) => {
  try {
    const { paymentMethodId } = req.params;

    await removePaymentMethod(paymentMethodId);

    logger.info(`Payment method removed for user: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Payment method removed successfully'
    });
  } catch (error) {
    logger.error('Remove payment method error:', error);
    res.status(500).json({
      success: false,
      error: error.message || RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

module.exports = {
  createBookingPaymentIntent,
  confirmBookingPayment,
  getUserPayments,
  getPaymentDetails,
  requestRefund,
  addUserPaymentMethod,
  getUserPaymentMethods,
  removeUserPaymentMethod
};
