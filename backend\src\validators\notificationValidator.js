const Joi = require('joi');
const { USER_ROLES } = require('../config/constants');

// Notification preferences validation
const notificationPreferencesValidation = Joi.object({
  email: Joi.boolean().default(true),
  sms: Joi.boolean().default(true),
  push: Joi.boolean().default(true),
  categories: Joi.object({
    booking: Joi.boolean().default(true),
    payment: Joi.boolean().default(true),
    promotion: Joi.boolean().default(true),
    system: Joi.boolean().default(true),
    security: Joi.boolean().default(true)
  }).optional(),
  quietHours: Joi.object({
    enabled: Joi.boolean().default(false),
    startTime: Joi.string()
      .pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .default('22:00')
      .messages({
        'string.pattern.base': 'Start time must be in HH:MM format'
      }),
    endTime: Joi.string()
      .pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
      .default('08:00')
      .messages({
        'string.pattern.base': 'End time must be in HH:MM format'
      })
  }).optional()
});

// Test notification validation
const testNotificationValidation = Joi.object({
  recipientId: Joi.string()
    .required()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .messages({
      'string.pattern.base': 'Invalid recipient ID format',
      'any.required': 'Recipient ID is required'
    }),
  title: Joi.string()
    .required()
    .trim()
    .min(1)
    .max(100)
    .messages({
      'string.min': 'Title is required',
      'string.max': 'Title cannot exceed 100 characters',
      'any.required': 'Title is required'
    }),
  message: Joi.string()
    .required()
    .trim()
    .min(1)
    .max(500)
    .messages({
      'string.min': 'Message is required',
      'string.max': 'Message cannot exceed 500 characters',
      'any.required': 'Message is required'
    }),
  channels: Joi.array()
    .items(Joi.string().valid('in_app', 'email', 'sms', 'push'))
    .min(1)
    .default(['in_app'])
    .messages({
      'array.min': 'At least one channel is required',
      'any.only': 'Invalid notification channel'
    }),
  category: Joi.string()
    .valid('booking', 'payment', 'promotion', 'system', 'security', 'general')
    .default('system')
    .messages({
      'any.only': 'Invalid notification category'
    }),
  priority: Joi.string()
    .valid('low', 'medium', 'high', 'urgent')
    .default('medium')
    .messages({
      'any.only': 'Invalid priority level'
    })
});

// Bulk notification validation
const bulkNotificationValidation = Joi.object({
  recipients: Joi.array()
    .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))
    .optional()
    .messages({
      'string.pattern.base': 'Invalid recipient ID format'
    }),
  title: Joi.string()
    .required()
    .trim()
    .min(1)
    .max(100)
    .messages({
      'string.min': 'Title is required',
      'string.max': 'Title cannot exceed 100 characters',
      'any.required': 'Title is required'
    }),
  message: Joi.string()
    .required()
    .trim()
    .min(1)
    .max(500)
    .messages({
      'string.min': 'Message is required',
      'string.max': 'Message cannot exceed 500 characters',
      'any.required': 'Message is required'
    }),
  channels: Joi.array()
    .items(Joi.string().valid('in_app', 'email', 'sms', 'push'))
    .min(1)
    .default(['in_app'])
    .messages({
      'array.min': 'At least one channel is required',
      'any.only': 'Invalid notification channel'
    }),
  category: Joi.string()
    .valid('booking', 'payment', 'promotion', 'system', 'security', 'announcement')
    .default('announcement')
    .messages({
      'any.only': 'Invalid notification category'
    }),
  priority: Joi.string()
    .valid('low', 'medium', 'high', 'urgent')
    .default('medium')
    .messages({
      'any.only': 'Invalid priority level'
    }),
  userRole: Joi.string()
    .valid(...Object.values(USER_ROLES))
    .optional()
    .messages({
      'any.only': 'Invalid user role'
    }),
  city: Joi.string()
    .optional()
    .trim()
    .max(50)
    .messages({
      'string.max': 'City name cannot exceed 50 characters'
    }),
  scheduledFor: Joi.date()
    .greater('now')
    .optional()
    .messages({
      'date.greater': 'Scheduled time must be in the future'
    })
});

// Notification query validation
const notificationQueryValidation = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(20),
  category: Joi.string()
    .valid('booking', 'payment', 'promotion', 'system', 'security', 'general', 'announcement')
    .optional(),
  priority: Joi.string()
    .valid('low', 'medium', 'high', 'urgent')
    .optional(),
  isRead: Joi.boolean()
    .optional(),
  startDate: Joi.date()
    .optional(),
  endDate: Joi.date()
    .greater(Joi.ref('startDate'))
    .optional()
    .messages({
      'date.greater': 'End date must be after start date'
    })
});

// Notification stats query validation
const notificationStatsQueryValidation = Joi.object({
  startDate: Joi.date()
    .optional(),
  endDate: Joi.date()
    .greater(Joi.ref('startDate'))
    .optional()
    .messages({
      'date.greater': 'End date must be after start date'
    }),
  groupBy: Joi.string()
    .valid('day', 'week', 'month')
    .default('day'),
  category: Joi.string()
    .valid('booking', 'payment', 'promotion', 'system', 'security', 'general', 'announcement')
    .optional()
});

// Push notification subscription validation
const pushSubscriptionValidation = Joi.object({
  endpoint: Joi.string()
    .uri()
    .required()
    .messages({
      'string.uri': 'Invalid endpoint URL',
      'any.required': 'Endpoint is required'
    }),
  keys: Joi.object({
    p256dh: Joi.string()
      .required()
      .messages({
        'any.required': 'p256dh key is required'
      }),
    auth: Joi.string()
      .required()
      .messages({
        'any.required': 'Auth key is required'
      })
  }).required(),
  userAgent: Joi.string()
    .optional()
    .max(500)
});

// Email template validation
const emailTemplateValidation = Joi.object({
  name: Joi.string()
    .required()
    .trim()
    .min(2)
    .max(100)
    .messages({
      'string.min': 'Template name must be at least 2 characters',
      'string.max': 'Template name cannot exceed 100 characters',
      'any.required': 'Template name is required'
    }),
  subject: Joi.string()
    .required()
    .trim()
    .min(1)
    .max(200)
    .messages({
      'string.min': 'Subject is required',
      'string.max': 'Subject cannot exceed 200 characters',
      'any.required': 'Subject is required'
    }),
  htmlContent: Joi.string()
    .required()
    .trim()
    .min(1)
    .messages({
      'string.min': 'HTML content is required',
      'any.required': 'HTML content is required'
    }),
  textContent: Joi.string()
    .optional()
    .trim(),
  category: Joi.string()
    .valid('booking', 'payment', 'promotion', 'system', 'security', 'welcome', 'reset_password')
    .required()
    .messages({
      'any.only': 'Invalid template category',
      'any.required': 'Template category is required'
    }),
  variables: Joi.array()
    .items(
      Joi.object({
        name: Joi.string().required(),
        description: Joi.string().optional(),
        required: Joi.boolean().default(false)
      })
    )
    .optional(),
  isActive: Joi.boolean()
    .default(true)
});

// Notification template validation
const notificationTemplateValidation = Joi.object({
  name: Joi.string()
    .required()
    .trim()
    .min(2)
    .max(100)
    .messages({
      'string.min': 'Template name must be at least 2 characters',
      'string.max': 'Template name cannot exceed 100 characters',
      'any.required': 'Template name is required'
    }),
  type: Joi.string()
    .required()
    .trim()
    .min(2)
    .max(50)
    .messages({
      'string.min': 'Template type must be at least 2 characters',
      'string.max': 'Template type cannot exceed 50 characters',
      'any.required': 'Template type is required'
    }),
  title: Joi.string()
    .required()
    .trim()
    .min(1)
    .max(100)
    .messages({
      'string.min': 'Title is required',
      'string.max': 'Title cannot exceed 100 characters',
      'any.required': 'Title is required'
    }),
  message: Joi.string()
    .required()
    .trim()
    .min(1)
    .max(500)
    .messages({
      'string.min': 'Message is required',
      'string.max': 'Message cannot exceed 500 characters',
      'any.required': 'Message is required'
    }),
  category: Joi.string()
    .valid('booking', 'payment', 'promotion', 'system', 'security', 'general')
    .required()
    .messages({
      'any.only': 'Invalid notification category',
      'any.required': 'Category is required'
    }),
  channels: Joi.array()
    .items(Joi.string().valid('in_app', 'email', 'sms', 'push'))
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one channel is required',
      'any.only': 'Invalid notification channel',
      'any.required': 'Channels are required'
    }),
  variables: Joi.array()
    .items(
      Joi.object({
        name: Joi.string().required(),
        description: Joi.string().optional(),
        required: Joi.boolean().default(false)
      })
    )
    .optional(),
  isActive: Joi.boolean()
    .default(true)
});

// Validation middleware
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors
      });
    }

    req.body = value;
    next();
  };
};

// Query validation middleware
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        error: 'Query validation failed',
        details: errors
      });
    }

    req.query = value;
    next();
  };
};

module.exports = {
  notificationPreferencesValidation,
  testNotificationValidation,
  bulkNotificationValidation,
  notificationQueryValidation,
  notificationStatsQueryValidation,
  pushSubscriptionValidation,
  emailTemplateValidation,
  notificationTemplateValidation,
  validate,
  validateQuery
};
