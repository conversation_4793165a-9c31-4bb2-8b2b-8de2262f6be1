const { Review, Booking, User, Driver } = require('../models');
const { RESPONSE_MESSAGES, BOOKING_STATUS, PAGINATION } = require('../config/constants');
const logger = require('../config/logger');

/**
 * Create review
 */
const createReview = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const {
      rating,
      comment,
      categories,
      tags,
      isAnonymous = false
    } = req.body;
    const userId = req.user._id;
    const userRole = req.user.role;

    // Find booking
    const booking = await Booking.findOne({ bookingId });
    if (!booking) {
      return res.status(404).json({
        success: false,
        error: 'Booking not found'
      });
    }

    // Check if booking is completed
    if (booking.status !== BOOKING_STATUS.COMPLETED) {
      return res.status(400).json({
        success: false,
        error: 'Can only review completed bookings'
      });
    }

    // Determine reviewee based on user role
    let revieweeId, revieweeType;
    if (userRole === 'user' && booking.user.toString() === userId.toString()) {
      revieweeId = booking.driver;
      revieweeType = 'driver';
    } else if (userRole === 'driver') {
      // Find driver profile
      const driver = await Driver.findOne({ user: userId });
      if (!driver || booking.driver.toString() !== driver._id.toString()) {
        return res.status(403).json({
          success: false,
          error: 'Access denied'
        });
      }
      revieweeId = booking.user;
      revieweeType = 'user';
    } else {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Check if review already exists
    const existingReview = await Review.findOne({
      booking: booking._id,
      reviewer: userId,
      reviewee: revieweeId
    });

    if (existingReview) {
      return res.status(400).json({
        success: false,
        error: 'Review already submitted for this booking'
      });
    }

    // Create review
    const review = new Review({
      booking: booking._id,
      reviewer: userId,
      reviewee: revieweeId,
      reviewerType: userRole,
      revieweeType,
      rating,
      comment,
      categories,
      tags,
      isAnonymous
    });

    await review.save();

    // Update booking with review
    if (userRole === 'user') {
      booking.rating.userRating = {
        score: rating,
        comment,
        ratedAt: new Date()
      };
    } else {
      booking.rating.driverRating = {
        score: rating,
        comment,
        ratedAt: new Date()
      };
    }

    await booking.save();

    // Update reviewee's average rating
    if (revieweeType === 'driver') {
      const driver = await Driver.findById(revieweeId);
      if (driver) {
        await driver.updateRating(rating);
      }
    } else {
      // Update user rating (if we track user ratings)
      const user = await User.findById(revieweeId);
      if (user) {
        // Calculate new average rating for user
        const userReviews = await Review.find({ 
          reviewee: revieweeId, 
          revieweeType: 'user',
          moderationStatus: 'approved'
        });
        
        if (userReviews.length > 0) {
          const totalRating = userReviews.reduce((sum, r) => sum + r.rating, 0);
          const averageRating = totalRating / userReviews.length;
          
          user.rating = {
            average: Math.round(averageRating * 100) / 100,
            count: userReviews.length
          };
          await user.save();
        }
      }
    }

    logger.info(`Review created for booking: ${bookingId} by ${userRole}: ${req.user.email}`);

    res.status(201).json({
      success: true,
      message: 'Review submitted successfully',
      data: { review }
    });
  } catch (error) {
    logger.error('Create review error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get reviews for user/driver
 */
const getReviews = async (req, res) => {
  try {
    const { userId } = req.params;
    const {
      page = PAGINATION.DEFAULT_PAGE,
      limit = PAGINATION.DEFAULT_LIMIT,
      rating,
      reviewerType,
      startDate,
      endDate
    } = req.query;

    const query = { 
      reviewee: userId,
      moderationStatus: 'approved'
    };
    
    if (rating) query.rating = parseInt(rating);
    if (reviewerType) query.reviewerType = reviewerType;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    const reviews = await Review.find(query)
      .populate('reviewer', 'firstName lastName profilePicture')
      .populate('booking', 'bookingId vehicleType')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Review.countDocuments(query);

    // Calculate rating statistics
    const ratingStats = await Review.aggregate([
      { $match: { reviewee: userId, moderationStatus: 'approved' } },
      {
        $group: {
          _id: '$rating',
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: -1 } }
    ]);

    const totalReviews = ratingStats.reduce((sum, stat) => sum + stat.count, 0);
    const averageRating = totalReviews > 0 
      ? ratingStats.reduce((sum, stat) => sum + (stat._id * stat.count), 0) / totalReviews 
      : 0;

    res.json({
      success: true,
      data: {
        reviews,
        statistics: {
          totalReviews,
          averageRating: Math.round(averageRating * 100) / 100,
          ratingDistribution: ratingStats
        },
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Get reviews error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Get review details
 */
const getReviewDetails = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;

    let query = { _id: reviewId };
    
    // Users can only see reviews they wrote or received
    if (userRole !== 'admin') {
      query.$or = [
        { reviewer: userId },
        { reviewee: userId }
      ];
    }

    const review = await Review.findOne(query)
      .populate('reviewer', 'firstName lastName profilePicture')
      .populate('reviewee', 'firstName lastName profilePicture')
      .populate('booking', 'bookingId vehicleType pickupLocation dropoffLocations')
      .populate('moderatedBy', 'firstName lastName email');

    if (!review) {
      return res.status(404).json({
        success: false,
        error: 'Review not found'
      });
    }

    res.json({
      success: true,
      data: { review }
    });
  } catch (error) {
    logger.error('Get review details error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Update review
 */
const updateReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { rating, comment, categories, tags } = req.body;
    const userId = req.user._id;

    // Find review
    const review = await Review.findOne({
      _id: reviewId,
      reviewer: userId
    });

    if (!review) {
      return res.status(404).json({
        success: false,
        error: 'Review not found or access denied'
      });
    }

    // Check if review can be updated (within 24 hours and not moderated)
    const hoursSinceCreation = (Date.now() - review.createdAt.getTime()) / (1000 * 60 * 60);
    if (hoursSinceCreation > 24) {
      return res.status(400).json({
        success: false,
        error: 'Reviews can only be updated within 24 hours of creation'
      });
    }

    if (review.moderationStatus !== 'pending') {
      return res.status(400).json({
        success: false,
        error: 'Cannot update a moderated review'
      });
    }

    // Update review
    review.rating = rating;
    review.comment = comment;
    review.categories = categories;
    review.tags = tags;
    review.updatedAt = new Date();

    await review.save();

    // Update booking rating
    const booking = await Booking.findById(review.booking);
    if (booking) {
      if (review.reviewerType === 'user') {
        booking.rating.userRating.score = rating;
        booking.rating.userRating.comment = comment;
      } else {
        booking.rating.driverRating.score = rating;
        booking.rating.driverRating.comment = comment;
      }
      await booking.save();
    }

    // Update reviewee's average rating
    if (review.revieweeType === 'driver') {
      const driver = await Driver.findById(review.reviewee);
      if (driver) {
        await driver.updateRating(rating);
      }
    }

    logger.info(`Review updated: ${reviewId} by user: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Review updated successfully',
      data: { review }
    });
  } catch (error) {
    logger.error('Update review error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Delete review
 */
const deleteReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;

    let query = { _id: reviewId };
    
    // Only reviewer or admin can delete
    if (userRole !== 'admin') {
      query.reviewer = userId;
    }

    const review = await Review.findOne(query);
    if (!review) {
      return res.status(404).json({
        success: false,
        error: 'Review not found or access denied'
      });
    }

    // Check if review can be deleted (within 24 hours for users)
    if (userRole !== 'admin') {
      const hoursSinceCreation = (Date.now() - review.createdAt.getTime()) / (1000 * 60 * 60);
      if (hoursSinceCreation > 24) {
        return res.status(400).json({
          success: false,
          error: 'Reviews can only be deleted within 24 hours of creation'
        });
      }
    }

    await Review.findByIdAndDelete(reviewId);

    // Update booking rating
    const booking = await Booking.findById(review.booking);
    if (booking) {
      if (review.reviewerType === 'user') {
        booking.rating.userRating = undefined;
      } else {
        booking.rating.driverRating = undefined;
      }
      await booking.save();
    }

    // Recalculate reviewee's average rating
    if (review.revieweeType === 'driver') {
      const driver = await Driver.findById(review.reviewee);
      if (driver) {
        const driverReviews = await Review.find({ 
          reviewee: review.reviewee, 
          revieweeType: 'driver',
          moderationStatus: 'approved'
        });
        
        if (driverReviews.length > 0) {
          const totalRating = driverReviews.reduce((sum, r) => sum + r.rating, 0);
          const averageRating = totalRating / driverReviews.length;
          
          driver.ratings = {
            average: Math.round(averageRating * 100) / 100,
            count: driverReviews.length
          };
        } else {
          driver.ratings = { average: 0, count: 0 };
        }
        
        await driver.save();
      }
    }

    logger.info(`Review deleted: ${reviewId} by ${userRole}: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error) {
    logger.error('Delete review error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

/**
 * Report review
 */
const reportReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { reason, description } = req.body;
    const userId = req.user._id;

    const review = await Review.findById(reviewId);
    if (!review) {
      return res.status(404).json({
        success: false,
        error: 'Review not found'
      });
    }

    // Check if user already reported this review
    const existingReport = review.reports.find(
      report => report.reportedBy.toString() === userId.toString()
    );

    if (existingReport) {
      return res.status(400).json({
        success: false,
        error: 'You have already reported this review'
      });
    }

    // Add report
    review.reports.push({
      reportedBy: userId,
      reason,
      description,
      reportedAt: new Date()
    });

    // If multiple reports, flag for moderation
    if (review.reports.length >= 3 && review.moderationStatus === 'approved') {
      review.moderationStatus = 'flagged';
    }

    await review.save();

    logger.info(`Review reported: ${reviewId} by user: ${req.user.email}`);

    res.json({
      success: true,
      message: 'Review reported successfully'
    });
  } catch (error) {
    logger.error('Report review error:', error);
    res.status(500).json({
      success: false,
      error: RESPONSE_MESSAGES.SERVER_ERROR
    });
  }
};

module.exports = {
  createReview,
  getReviews,
  getReviewDetails,
  updateReview,
  deleteReview,
  reportReview
};
