{"version": 3, "sources": ["../src/index.js"], "names": ["module", "exports", "req", "res", "next", "body", "query", "params"], "mappings": ";;AAAA;;AAMAA,OAAOC,OAAP,GAAiB,YAAY;AAC3B,SAAO,UAACC,GAAD,EAAMC,GAAN,EAAWC,IAAX,EAAoB;AACzB,QAAIF,IAAIG,IAAR,EAAcH,IAAIG,IAAJ,GAAW,gBAAMH,IAAIG,IAAV,CAAX;AACd,QAAIH,IAAII,KAAR,EAAeJ,IAAII,KAAJ,GAAY,gBAAMJ,IAAII,KAAV,CAAZ;AACf,QAAIJ,IAAIK,MAAR,EAAgBL,IAAIK,MAAJ,GAAa,gBAAML,IAAIK,MAAV,CAAb;;AAEhBH;AACD,GAND;AAOD,CARD", "file": "index.js", "sourcesContent": ["import { clean } from './xss'\n\n/**\n * export middleware\n * @return {function} Middleware function\n */\nmodule.exports = function () {\n  return (req, res, next) => {\n    if (req.body) req.body = clean(req.body)\n    if (req.query) req.query = clean(req.query)\n    if (req.params) req.params = clean(req.params)\n\n    next()\n  }\n}\n"]}