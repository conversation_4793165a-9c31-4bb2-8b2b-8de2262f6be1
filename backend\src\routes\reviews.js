const express = require('express');
const router = express.Router();

// Import controllers
const {
  createReview,
  getReviews,
  getReviewDetails,
  updateReview,
  deleteReview,
  reportReview
} = require('../controllers/reviewController');

// Import middleware
const {
  authenticate,
  authorize
} = require('../middleware/auth');

// Import validators
const {
  validate,
  validateQuery,
  createReviewValidation,
  updateReviewValidation,
  reportReviewValidation,
  reviewQueryValidation
} = require('../validators/reviewValidator');

/**
 * @route   POST /api/v1/reviews/bookings/:bookingId
 * @desc    Create review for booking
 * @access  Private
 */
router.post('/bookings/:bookingId',
  authenticate,
  validate(createReviewValidation),
  createReview
);

/**
 * @route   GET /api/v1/reviews/users/:userId
 * @desc    Get reviews for user/driver
 * @access  Public
 */
router.get('/users/:userId',
  validate<PERSON>uery(reviewQueryValidation),
  getReviews
);

/**
 * @route   GET /api/v1/reviews/:reviewId
 * @desc    Get review details
 * @access  Private
 */
router.get('/:reviewId',
  authenticate,
  getReviewDetails
);

/**
 * @route   PUT /api/v1/reviews/:reviewId
 * @desc    Update review
 * @access  Private
 */
router.put('/:reviewId',
  authenticate,
  validate(updateReviewValidation),
  updateReview
);

/**
 * @route   DELETE /api/v1/reviews/:reviewId
 * @desc    Delete review
 * @access  Private
 */
router.delete('/:reviewId',
  authenticate,
  deleteReview
);

/**
 * @route   POST /api/v1/reviews/:reviewId/report
 * @desc    Report review
 * @access  Private
 */
router.post('/:reviewId/report',
  authenticate,
  validate(reportReviewValidation),
  reportReview
);

module.exports = router;
