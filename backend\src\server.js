require('dotenv').config();
require('express-async-errors');

// Check if we should run in cluster mode (import after dotenv)
const { setupCluster } = require('./middleware/performance');
if (process.env.NODE_ENV === 'production' && process.env.CLUSTER_MODE === 'true') {
  if (!setupCluster()) {
    return; // Exit if this is the master process
  }
}

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
const socketService = require('./services/socketService');

// Import security and performance middleware
const {
  generalRateLimit,
  sanitizeInput,
  securityHeaders,
  requestLogger,
  suspiciousActivityDetector,
  corsOptions
} = require('./middleware/security');

const {
  performanceMonitor,
  memoryMonitor,
  cpuMonitor,
  RequestQueueMonitor,
  ErrorRateMonitor,
  healthCheck,
  gracefulShutdown,
  setupCluster
} = require('./middleware/performance');

// Import configurations
const connectDB = require('./config/database');
const { connectRedis } = require('./config/redis');
const logger = require('./config/logger');
const { RATE_LIMITS } = require('./config/constants');

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const driverRoutes = require('./routes/drivers');
const bookingRoutes = require('./routes/bookings');
const paymentRoutes = require('./routes/payments');
const trackingRoutes = require('./routes/tracking');
const notificationRoutes = require('./routes/notifications');
const reviewRoutes = require('./routes/reviews');
const webhookRoutes = require('./routes/webhooks');
const adminRoutes = require('./routes/admin');

// Socket service will be initialized after server creation

const app = express();
const server = createServer(app);

// Initialize performance monitoring
const requestQueueMonitor = new RequestQueueMonitor();
const errorRateMonitor = new ErrorRateMonitor();

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Connect to databases
connectDB();
connectRedis();

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));
app.use(securityHeaders);
app.use(suspiciousActivityDetector);

// CORS configuration
app.use(cors(corsOptions));

// Rate limiting and performance monitoring
app.use(generalRateLimit);
app.use(performanceMonitor);
app.use(requestQueueMonitor.middleware());
app.use(errorRateMonitor.middleware());

// Webhook route (before body parser middleware for raw body)
app.use(`${API_PREFIX}/webhooks`, express.raw({ type: 'application/json' }), webhookRoutes);

// Input sanitization
app.use(sanitizeInput);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Compression middleware
app.use(compression());

// Logging middleware
app.use(requestLogger);
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// Health check endpoint
app.get('/health', healthCheck);

// API routes
const API_PREFIX = `/api/${process.env.API_VERSION || 'v1'}`;

app.use(`${API_PREFIX}/auth`, authRoutes);
app.use(`${API_PREFIX}/users`, userRoutes);
app.use(`${API_PREFIX}/drivers`, driverRoutes);
app.use(`${API_PREFIX}/bookings`, bookingRoutes);
app.use(`${API_PREFIX}/payments`, paymentRoutes);
app.use(`${API_PREFIX}/tracking`, trackingRoutes);
app.use(`${API_PREFIX}/notifications`, notificationRoutes);
app.use(`${API_PREFIX}/reviews`, reviewRoutes);
app.use(`${API_PREFIX}/admin`, adminRoutes);

// Initialize Socket.IO service
socketService.init(server);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 5000;

server.listen(PORT, () => {
  logger.info(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
  logger.info(`API endpoints available at http://localhost:${PORT}${API_PREFIX}`);
  logger.info(`Health check available at http://localhost:${PORT}/health`);

  // Start monitoring services
  memoryMonitor();
  cpuMonitor();

  logger.info('Performance monitoring started');
});

// Setup graceful shutdown
gracefulShutdown(server);

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  server.close(() => {
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

module.exports = { app, server, io };
