const express = require('express');
const router = express.Router();

// Import controllers
const {
  handleStripeWebhook,
  getPaymentWebhookEvents
} = require('../controllers/webhookController');

// Import middleware
const { authenticate, authorize } = require('../middleware/auth');

/**
 * @route   POST /api/v1/webhooks/stripe
 * @desc    Handle Stripe webhooks
 * @access  Public (Stripe webhook)
 */
router.post('/stripe', handleStripeWebhook);

/**
 * @route   GET /api/v1/webhooks/payments/:paymentId/events
 * @desc    Get webhook events for payment (Admin only)
 * @access  Private (Admin only)
 */
router.get('/payments/:paymentId/events',
  authenticate,
  authorize('admin'),
  getPaymentWebhookEvents
);

module.exports = router;
