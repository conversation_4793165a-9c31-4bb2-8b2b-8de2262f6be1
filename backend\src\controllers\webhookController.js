const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { Payment, Booking } = require('../models');
const { PAYMENT_STATUS } = require('../config/constants');
const logger = require('../config/logger');

/**
 * Handle Stripe webhooks
 */
const handleStripeWebhook = async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    // Verify webhook signature
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    logger.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  try {
    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object);
        break;
      
      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object);
        break;
      
      case 'payment_intent.canceled':
        await handlePaymentIntentCanceled(event.data.object);
        break;
      
      case 'charge.dispute.created':
        await handleChargeDisputeCreated(event.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
      
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;
      
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      
      default:
        logger.info(`Unhandled event type: ${event.type}`);
    }

    // Log webhook event
    await logWebhookEvent(event);

    res.json({ received: true });
  } catch (error) {
    logger.error('Webhook handling error:', error);
    res.status(500).json({ error: 'Webhook handling failed' });
  }
};

/**
 * Handle successful payment intent
 */
const handlePaymentIntentSucceeded = async (paymentIntent) => {
  try {
    const payment = await Payment.findOne({ 
      stripePaymentIntentId: paymentIntent.id 
    });

    if (payment) {
      await payment.updateStatus(PAYMENT_STATUS.COMPLETED, {
        gatewayResponse: paymentIntent
      });

      // Update booking status
      const booking = await Booking.findById(payment.booking);
      if (booking) {
        booking.paymentStatus = PAYMENT_STATUS.COMPLETED;
        await booking.save();

        // Update driver earnings if applicable
        if (payment.driver) {
          const { Driver } = require('../models');
          const driver = await Driver.findById(payment.driver);
          if (driver) {
            driver.earnings.totalEarnings += payment.breakdown.driverEarnings;
            driver.earnings.currentBalance += payment.breakdown.driverEarnings;
            await driver.save();
          }
        }
      }

      logger.info(`Payment succeeded: ${payment.paymentId}`);
    }
  } catch (error) {
    logger.error('Handle payment intent succeeded error:', error);
  }
};

/**
 * Handle failed payment intent
 */
const handlePaymentIntentFailed = async (paymentIntent) => {
  try {
    const payment = await Payment.findOne({ 
      stripePaymentIntentId: paymentIntent.id 
    });

    if (payment) {
      await payment.updateStatus(PAYMENT_STATUS.FAILED, {
        gatewayResponse: paymentIntent,
        failureReason: paymentIntent.last_payment_error?.message
      });

      // Update booking status
      const booking = await Booking.findById(payment.booking);
      if (booking) {
        booking.paymentStatus = PAYMENT_STATUS.FAILED;
        await booking.save();
      }

      logger.info(`Payment failed: ${payment.paymentId}`);
    }
  } catch (error) {
    logger.error('Handle payment intent failed error:', error);
  }
};

/**
 * Handle canceled payment intent
 */
const handlePaymentIntentCanceled = async (paymentIntent) => {
  try {
    const payment = await Payment.findOne({ 
      stripePaymentIntentId: paymentIntent.id 
    });

    if (payment) {
      await payment.updateStatus(PAYMENT_STATUS.CANCELLED, {
        gatewayResponse: paymentIntent
      });

      // Update booking status
      const booking = await Booking.findById(payment.booking);
      if (booking) {
        booking.paymentStatus = PAYMENT_STATUS.CANCELLED;
        await booking.save();
      }

      logger.info(`Payment canceled: ${payment.paymentId}`);
    }
  } catch (error) {
    logger.error('Handle payment intent canceled error:', error);
  }
};

/**
 * Handle charge dispute created
 */
const handleChargeDisputeCreated = async (dispute) => {
  try {
    // Find payment by charge ID
    const payment = await Payment.findOne({ 
      stripeChargeId: dispute.charge 
    });

    if (payment) {
      // Add dispute information to payment
      payment.disputes = payment.disputes || [];
      payment.disputes.push({
        disputeId: dispute.id,
        reason: dispute.reason,
        status: dispute.status,
        amount: dispute.amount / 100,
        createdAt: new Date(dispute.created * 1000),
        evidence: dispute.evidence
      });

      await payment.save();

      // TODO: Send notification to admin and user
      logger.warn(`Dispute created for payment: ${payment.paymentId}`);
    }
  } catch (error) {
    logger.error('Handle charge dispute created error:', error);
  }
};

/**
 * Handle successful invoice payment
 */
const handleInvoicePaymentSucceeded = async (invoice) => {
  try {
    // Handle subscription or invoice payments
    logger.info(`Invoice payment succeeded: ${invoice.id}`);
    
    // TODO: Update subscription status or handle invoice-specific logic
  } catch (error) {
    logger.error('Handle invoice payment succeeded error:', error);
  }
};

/**
 * Handle failed invoice payment
 */
const handleInvoicePaymentFailed = async (invoice) => {
  try {
    // Handle failed subscription or invoice payments
    logger.warn(`Invoice payment failed: ${invoice.id}`);
    
    // TODO: Handle failed subscription payment, send notifications
  } catch (error) {
    logger.error('Handle invoice payment failed error:', error);
  }
};

/**
 * Handle subscription created
 */
const handleSubscriptionCreated = async (subscription) => {
  try {
    // Handle new subscription
    logger.info(`Subscription created: ${subscription.id}`);
    
    // TODO: Update user subscription status
  } catch (error) {
    logger.error('Handle subscription created error:', error);
  }
};

/**
 * Handle subscription updated
 */
const handleSubscriptionUpdated = async (subscription) => {
  try {
    // Handle subscription updates
    logger.info(`Subscription updated: ${subscription.id}`);
    
    // TODO: Update user subscription status
  } catch (error) {
    logger.error('Handle subscription updated error:', error);
  }
};

/**
 * Handle subscription deleted
 */
const handleSubscriptionDeleted = async (subscription) => {
  try {
    // Handle subscription cancellation
    logger.info(`Subscription deleted: ${subscription.id}`);
    
    // TODO: Update user subscription status
  } catch (error) {
    logger.error('Handle subscription deleted error:', error);
  }
};

/**
 * Log webhook event
 */
const logWebhookEvent = async (event) => {
  try {
    // Find related payment if applicable
    let paymentId = null;
    if (event.data.object.payment_intent) {
      const payment = await Payment.findOne({ 
        stripePaymentIntentId: event.data.object.payment_intent 
      });
      if (payment) {
        paymentId = payment._id;
        
        // Add webhook event to payment
        payment.webhookEvents.push({
          eventType: event.type,
          eventId: event.id,
          processedAt: new Date(),
          data: event.data.object
        });
        
        await payment.save();
      }
    }

    logger.info(`Webhook processed: ${event.type} - ${event.id}`);
  } catch (error) {
    logger.error('Log webhook event error:', error);
  }
};

/**
 * Get webhook events for payment
 */
const getPaymentWebhookEvents = async (req, res) => {
  try {
    const { paymentId } = req.params;

    const payment = await Payment.findOne({ paymentId })
      .select('webhookEvents');

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    res.json({
      success: true,
      data: {
        events: payment.webhookEvents.sort((a, b) => b.processedAt - a.processedAt)
      }
    });
  } catch (error) {
    logger.error('Get payment webhook events error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve webhook events'
    });
  }
};

module.exports = {
  handleStripeWebhook,
  getPaymentWebhookEvents
};
