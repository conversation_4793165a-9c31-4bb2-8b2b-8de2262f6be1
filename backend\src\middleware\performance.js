const cluster = require('cluster');
const os = require('os');
const logger = require('../config/logger');

/**
 * Performance monitoring middleware
 */
const performanceMonitor = (req, res, next) => {
  const start = process.hrtime.bigint();
  const startMemory = process.memoryUsage();
  
  res.on('finish', () => {
    const end = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const responseTime = Number(end - start) / 1000000; // Convert to milliseconds
    const memoryDelta = {
      rss: endMemory.rss - startMemory.rss,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      external: endMemory.external - startMemory.external
    };
    
    // Log slow requests (> 1 second)
    if (responseTime > 1000) {
      logger.warn('Slow request detected', {
        method: req.method,
        url: req.originalUrl,
        responseTime: `${responseTime.toFixed(2)}ms`,
        statusCode: res.statusCode,
        memoryDelta,
        userId: req.user?._id
      });
    }
    
    // Add performance headers
    res.setHeader('X-Response-Time', `${responseTime.toFixed(2)}ms`);
    res.setHeader('X-Memory-Usage', `${(endMemory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
  });
  
  next();
};

/**
 * Memory usage monitoring
 */
const memoryMonitor = () => {
  setInterval(() => {
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024)
    };
    
    // Log memory usage every 5 minutes
    logger.info('Memory usage', memoryUsageMB);
    
    // Alert if memory usage is high (> 80% of heap)
    const heapUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    if (heapUsagePercent > 80) {
      logger.warn('High memory usage detected', {
        heapUsagePercent: `${heapUsagePercent.toFixed(2)}%`,
        ...memoryUsageMB
      });
    }
  }, 5 * 60 * 1000); // Every 5 minutes
};

/**
 * CPU usage monitoring
 */
const cpuMonitor = () => {
  let lastCpuUsage = process.cpuUsage();
  
  setInterval(() => {
    const currentCpuUsage = process.cpuUsage(lastCpuUsage);
    const cpuPercent = (currentCpuUsage.user + currentCpuUsage.system) / 1000000; // Convert to seconds
    
    logger.info('CPU usage', {
      user: `${(currentCpuUsage.user / 1000000).toFixed(2)}s`,
      system: `${(currentCpuUsage.system / 1000000).toFixed(2)}s`,
      total: `${cpuPercent.toFixed(2)}s`
    });
    
    lastCpuUsage = process.cpuUsage();
  }, 5 * 60 * 1000); // Every 5 minutes
};

/**
 * Request queue monitoring
 */
class RequestQueueMonitor {
  constructor() {
    this.activeRequests = 0;
    this.maxConcurrentRequests = 0;
    this.totalRequests = 0;
    this.queuedRequests = 0;
  }
  
  middleware() {
    return (req, res, next) => {
      this.activeRequests++;
      this.totalRequests++;
      this.maxConcurrentRequests = Math.max(this.maxConcurrentRequests, this.activeRequests);
      
      // Check if we're approaching limits
      if (this.activeRequests > 100) {
        logger.warn('High concurrent request count', {
          activeRequests: this.activeRequests,
          maxConcurrentRequests: this.maxConcurrentRequests
        });
      }
      
      res.on('finish', () => {
        this.activeRequests--;
      });
      
      next();
    };
  }
  
  getStats() {
    return {
      activeRequests: this.activeRequests,
      maxConcurrentRequests: this.maxConcurrentRequests,
      totalRequests: this.totalRequests,
      queuedRequests: this.queuedRequests
    };
  }
}

/**
 * Database connection monitoring
 */
const dbConnectionMonitor = (mongoose) => {
  mongoose.connection.on('connected', () => {
    logger.info('Database connected successfully');
  });
  
  mongoose.connection.on('error', (err) => {
    logger.error('Database connection error:', err);
  });
  
  mongoose.connection.on('disconnected', () => {
    logger.warn('Database disconnected');
  });
  
  // Monitor connection pool
  setInterval(() => {
    const db = mongoose.connection.db;
    if (db) {
      const stats = {
        readyState: mongoose.connection.readyState,
        host: mongoose.connection.host,
        port: mongoose.connection.port,
        name: mongoose.connection.name
      };
      
      logger.info('Database connection stats', stats);
    }
  }, 10 * 60 * 1000); // Every 10 minutes
};

/**
 * Error rate monitoring
 */
class ErrorRateMonitor {
  constructor() {
    this.errors = [];
    this.requests = [];
    this.windowSize = 5 * 60 * 1000; // 5 minutes
  }
  
  middleware() {
    return (req, res, next) => {
      const now = Date.now();
      this.requests.push(now);
      
      res.on('finish', () => {
        if (res.statusCode >= 400) {
          this.errors.push(now);
        }
        
        // Clean old entries
        this.cleanOldEntries(now);
        
        // Check error rate
        const errorRate = this.getErrorRate();
        if (errorRate > 0.1) { // More than 10% error rate
          logger.warn('High error rate detected', {
            errorRate: `${(errorRate * 100).toFixed(2)}%`,
            errors: this.errors.length,
            requests: this.requests.length,
            windowSize: `${this.windowSize / 1000}s`
          });
        }
      });
      
      next();
    };
  }
  
  cleanOldEntries(now) {
    const cutoff = now - this.windowSize;
    this.errors = this.errors.filter(time => time > cutoff);
    this.requests = this.requests.filter(time => time > cutoff);
  }
  
  getErrorRate() {
    if (this.requests.length === 0) return 0;
    return this.errors.length / this.requests.length;
  }
  
  getStats() {
    return {
      errorRate: this.getErrorRate(),
      errors: this.errors.length,
      requests: this.requests.length,
      windowSize: this.windowSize
    };
  }
}

/**
 * Health check endpoint
 */
const healthCheck = async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      version: process.version,
      environment: process.env.NODE_ENV,
      pid: process.pid
    };
    
    // Check database connection
    const mongoose = require('mongoose');
    if (mongoose.connection.readyState === 1) {
      health.database = 'connected';
    } else {
      health.database = 'disconnected';
      health.status = 'unhealthy';
    }
    
    // Check memory usage
    const memoryUsagePercent = (health.memory.heapUsed / health.memory.heapTotal) * 100;
    if (memoryUsagePercent > 90) {
      health.status = 'unhealthy';
      health.warnings = health.warnings || [];
      health.warnings.push('High memory usage');
    }
    
    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Graceful shutdown handler
 */
const gracefulShutdown = (server) => {
  const shutdown = (signal) => {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);
    
    server.close(() => {
      logger.info('HTTP server closed');
      
      // Close database connections
      const mongoose = require('mongoose');
      mongoose.connection.close(() => {
        logger.info('Database connection closed');
        process.exit(0);
      });
    });
    
    // Force close after 30 seconds
    setTimeout(() => {
      logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 30000);
  };
  
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
};

/**
 * Cluster management for production
 */
const setupCluster = () => {
  if (cluster.isMaster) {
    const numCPUs = os.cpus().length;
    logger.info(`Master ${process.pid} is running`);
    logger.info(`Starting ${numCPUs} workers`);
    
    // Fork workers
    for (let i = 0; i < numCPUs; i++) {
      cluster.fork();
    }
    
    cluster.on('exit', (worker, code, signal) => {
      logger.warn(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`);
      logger.info('Starting a new worker');
      cluster.fork();
    });
    
    return false; // Don't start the app in master process
  } else {
    logger.info(`Worker ${process.pid} started`);
    return true; // Start the app in worker process
  }
};

module.exports = {
  performanceMonitor,
  memoryMonitor,
  cpuMonitor,
  RequestQueueMonitor,
  dbConnectionMonitor,
  ErrorRateMonitor,
  healthCheck,
  gracefulShutdown,
  setupCluster
};
